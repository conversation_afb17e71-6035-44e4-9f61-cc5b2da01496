// utils/uploadUtils.ts

/**
 * Validate a file for upload
 */
export const validateFile = (file: File, mediaType: 'photo' | 'video'): { isValid: boolean; error?: string } => {
  // Check if file exists
  if (!file) {
    return {
      isValid: false,
      error: 'No file selected.',
    };
  }

  // Check file type based on mediaType
  if (mediaType === 'photo') {
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file format. Please upload a JPEG, PNG, GIF, or WebP image.',
      };
    }
    
    // No size limit for photos
  } else if (mediaType === 'video') {
    const validTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'];
    if (!validTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file format. Please upload an MP4, MOV, AVI, or WebM video.',
      };
    }
    
    // No size limit for videos
  }

  return { isValid: true };
};

  /**
   * Get the video duration from a video file
   * @param videoFile The video file
   * @returns A promise that resolves to the duration in seconds
   */
  export const getVideoDuration = (videoFile: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      try {
        const videoElement = document.createElement('video');
        videoElement.preload = 'metadata';

        videoElement.onloadedmetadata = () => {
          URL.revokeObjectURL(videoElement.src);
          resolve(videoElement.duration);
        };

        videoElement.onerror = () => {
          URL.revokeObjectURL(videoElement.src);
          reject(new Error('Error loading video file'));
        };

        videoElement.src = URL.createObjectURL(videoFile);
      } catch (error) {
        reject(error);
      }
    });
  };

  /**
   * Format file size into a human-readable string
   * @param bytes Size in bytes
   * @returns Formatted string (e.g., "1.5 MB")
   */
  export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Extract maximum allowed size for a media type and category
   * Now returns Infinity for all types to allow unlimited uploads
   */
  export const getMaxFileSize = (mediaType: 'photo' | 'video', category: string): number => {
    return Infinity; // No size limit
  };

  /**
   * Get maximum video duration for a video category
   * @param category The video category
   * @returns Max duration in seconds, or undefined if not applicable
   */
  export const getMaxVideoDuration = (category: string): number | undefined => {
    return VIDEO_RESTRICTIONS[category]?.max_duration_seconds;
  };

  /**
   * Get minimum video duration for a video category
   * @param category The video category
   * @returns Min duration in seconds, or undefined if not applicable
   */
  export const getMinVideoDuration = (category: string): number | undefined => {
    return VIDEO_RESTRICTIONS[category]?.min_duration_seconds;
  };

  // Note: formatTime function is defined later in this file

  /**
   * Validate if a video duration is appropriate for a given category
   * @param duration Video duration in seconds
   * @param category The video category
   * @returns Validation result with error message if invalid and suggested category
   */
  export const validateVideoDuration = (duration: number, category: string): {
    isValid: boolean;
    error?: string;
    suggestedCategory?: string;
  } => {
    const minDuration = getMinVideoDuration(category);
    const maxDuration = getMaxVideoDuration(category);

    if (minDuration === undefined || maxDuration === undefined) {
      return { isValid: true }; // No restrictions for this category
    }

    // Get user-friendly category names
    const getCategoryDisplayName = (cat: string): string => {
      switch (cat) {
        case 'story': return 'Moments';
        case 'flash': return 'Flash';
        case 'glimpse': return 'Glimpse';
        case 'movie': return 'Movie';
        default: return cat;
      }
    };

    const categoryDisplayName = getCategoryDisplayName(category);

    // Find the appropriate category for this duration, but never suggest 'story' (moments) for other categories
    let appropriateCategory = Object.entries(VIDEO_RESTRICTIONS).find(([cat, restrictions]) =>
      cat !== 'story' && // Never suggest 'story' (moments) for other categories
      restrictions.min_duration_seconds <= duration &&
      restrictions.max_duration_seconds >= duration
    )?.[0];

    if (duration > maxDuration) {
      let errorMessage = `Your video is ${formatTime(duration)} long, which is too long for the ${categoryDisplayName} category.`;
      errorMessage += `\n\nMaximum duration for ${categoryDisplayName} is ${formatTime(maxDuration)}.`;

      if (appropriateCategory) {
        const appropriateCategoryDisplay = getCategoryDisplayName(appropriateCategory);
        errorMessage += `\n\nThis video would fit better in the ${appropriateCategoryDisplay} category.`;
      }

      return {
        isValid: false,
        error: errorMessage,
        suggestedCategory: appropriateCategory
      };
    }

    // If we have a more appropriate category but the video still fits in the current category,
    // suggest the better category but don't mark it as invalid
    // Never suggest 'story' (moments) for other categories
    if (appropriateCategory && appropriateCategory !== category && appropriateCategory !== 'story') {
      const appropriateCategoryDisplay = getCategoryDisplayName(appropriateCategory);
      const message = `Your video is ${formatTime(duration)} long. It would fit better in the ${appropriateCategoryDisplay} category, but it's still valid for ${categoryDisplayName}.`;

      return {
        isValid: true,
        error: message,
        suggestedCategory: appropriateCategory
      };
    }

    return { isValid: true };
  };

  /**
   * Format time in seconds to a readable format (MM:SS or HH:MM:SS)
   * @param seconds Time in seconds
   * @returns Formatted time string
   */
  export const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  /**
   * Get a list of suggested tags based on file metadata and category
   * @param file The file being uploaded
   * @param category The selected category
   * @returns Array of suggested tags
   */
  export const getSuggestedTags = (file: File, category: string): string[] => {
    const suggestions: string[] = [];

    // Add category as a tag
    suggestions.push(category);

    // Add file type as a tag
    if (file.type.startsWith('image/')) {
      suggestions.push('photo');

      // Add specific image type
      const format = file.type.split('/')[1];
      if (format) {
        suggestions.push(format);
      }
    } else if (file.type.startsWith('video/')) {
      suggestions.push('video');

      // Add specific video type
      const format = file.type.split('/')[1];
      if (format) {
        suggestions.push(format);
      }
    }

    // Add suggestions based on category
    switch (category) {
      case 'story':
        suggestions.push('moments', 'daily', 'highlight');
        break;
      case 'glimpse':
        suggestions.push('preview', 'teaser', 'sneak-peek');
        break;
      case 'short':
        suggestions.push('clip', 'short-form', 'brief');
        break;
      case 'long':
        suggestions.push('full-length', 'documentary', 'extended');
        break;
      case 'post':
        suggestions.push('album', 'gallery', 'collection');
        break;
    }

    return [...new Set(suggestions)]; // Remove duplicates
  };
