// utils/uploadErrorHandler.ts

/**
 * Handles upload errors and provides user-friendly error messages
 * @param error The error object from the upload attempt
 * @returns A user-friendly error message
 */
export const handleUploadError = (error: any): string => {
  console.error('Upload error details:', error);

  // Default error message
  let errorMessage = 'Failed to upload file. Please try again.';

  // Check for network errors
  if (error.message) {
    if (error.message.includes('Network Error')) {
      errorMessage = 'Network connection issue detected. Please check your internet connection and try again.';
    } else if (error.message.includes('timeout')) {
      errorMessage = 'The upload timed out. This may be due to a slow internet connection or a very large file.';
    } else if (error.message.includes('CORS')) {
      errorMessage = 'Cross-origin request blocked. This is a technical issue - please try again or contact support.';
    } else {
      errorMessage = `Upload error: ${error.message}`;
    }
  }

  // Check for HTTP status code errors
  if (error.response) {
    console.error('Response status:', error.response.status);

    if (error.response.status === 403) {
      errorMessage = 'Permission denied. The upload URL may have expired.';
    } else if (error.response.status === 413) {
      errorMessage = 'The file is too large for upload. Please try a smaller file or compress it.';
    } else if (error.response.status >= 500) {
      errorMessage = 'Server error. Please try again later or contact support.';
    }
  }

  // If error has a custom error property (from our API)
  if (error.error) {
    errorMessage = error.error;
  }

  return errorMessage;
};

/**
 * Determines if an error is likely to be resolved by retrying
 * @param error The error object from the upload attempt
 * @returns True if the error is retriable, false otherwise
 */
export const isRetriableError = (error: any): boolean => {
  // Network errors are generally retriable
  if (error.message && (
    error.message.includes('Network Error') ||
    error.message.includes('timeout') ||
    error.message.includes('CORS') ||
    error.message.includes('socket')
  )) {
    return true;
  }

  // Server errors (5xx) are generally retriable
  if (error.response && error.response.status >= 500) {
    return true;
  }

  // 429 Too Many Requests is retriable after a delay
  if (error.response && error.response.status === 429) {
    return true;
  }

  return false;
};

/**
 * Calculates the delay before retrying an upload
 * @param retryCount The current retry attempt number
 * @param error The error that occurred
 * @returns Delay in milliseconds before the next retry
 */
export const getRetryDelay = (retryCount: number, error?: any): number => {
  // Base delay with exponential backoff
  let delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Max 30 seconds

  // If we have a 429 response with a Retry-After header, use that
  if (error?.response?.status === 429 && error.response.headers['retry-after']) {
    const retryAfter = parseInt(error.response.headers['retry-after'], 10);
    if (!isNaN(retryAfter)) {
      delay = retryAfter * 1000; // Convert to milliseconds
    }
  }

  // Add some jitter to prevent all clients retrying simultaneously
  delay += Math.random() * 1000;

  return delay;
};
