import axios from 'axios';

// Constants for multipart upload
const CHUNK_SIZE = 120 * 1024 * 1024; // 120MB chunks (minimum size as requested)
const MAX_CONCURRENT_UPLOADS = 6; // Increased number of concurrent chunk uploads for better performance

interface MultipartUploadProgress {
  totalBytes: number;
  uploadedBytes: number;
  percentage: number;
  speed: number; // bytes per second
}

/**
 * Handles multipart upload of large files directly to S3
 */
export async function multipartUpload(
  file: File,
  presignedUrl: string,
  onProgress?: (progress: MultipartUploadProgress) => void
): Promise<void> {
  // Calculate total chunks
  const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
  console.log(`Starting multipart upload with ${totalChunks} chunks of ${CHUNK_SIZE / (1024 * 1024)}MB each`);

  // Track upload progress
  let uploadedBytes = 0;
  let startTime = Date.now();
  let uploadSpeed = 0;

  // Function to update progress
  const updateProgress = (chunkSize: number) => {
    uploadedBytes += chunkSize;
    const elapsedSeconds = (Date.now() - startTime) / 1000;
    uploadSpeed = elapsedSeconds > 0 ? uploadedBytes / elapsedSeconds : 0;

    if (onProgress) {
      onProgress({
        totalBytes: file.size,
        uploadedBytes,
        percentage: Math.round((uploadedBytes / file.size) * 100),
        speed: uploadSpeed
      });
    }
  };

  // Extract the base URL and query parameters
  const url = new URL(presignedUrl);
  const baseUrl = `${url.protocol}//${url.host}${url.pathname}`;
  const queryParams = Object.fromEntries(url.searchParams.entries());

  // Process chunks with limited concurrency
  const chunks: Blob[] = [];
  for (let i = 0; i < totalChunks; i++) {
    const start = i * CHUNK_SIZE;
    const end = Math.min(file.size, start + CHUNK_SIZE);
    chunks.push(file.slice(start, end));
  }

  // Process chunks in batches to limit concurrency
  for (let i = 0; i < totalChunks; i += MAX_CONCURRENT_UPLOADS) {
    const batch = chunks.slice(i, i + MAX_CONCURRENT_UPLOADS);
    const batchPromises = batch.map((chunk, index) => {
      const chunkNumber = i + index;
      return uploadChunk(
        chunk,
        baseUrl,
        queryParams,
        chunkNumber,
        totalChunks,
        () => updateProgress(chunk.size)
      );
    });

    // Wait for the current batch to complete before starting the next batch
    await Promise.all(batchPromises);
  }

  console.log(`Multipart upload completed successfully. Average speed: ${(uploadSpeed / (1024 * 1024)).toFixed(2)} MB/s`);
}

/**
 * Uploads a single chunk to S3
 */
async function uploadChunk(
  chunk: Blob,
  baseUrl: string,
  queryParams: Record<string, string>,
  chunkNumber: number,
  totalChunks: number,
  onComplete: () => void
): Promise<void> {
  // Add chunk information to query parameters
  const chunkParams = {
    ...queryParams,
    partNumber: (chunkNumber + 1).toString(),
    uploadId: queryParams.uploadId || 'direct-upload',
  };

  // Build the URL with query parameters
  const queryString = Object.entries(chunkParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  const uploadUrl = `${baseUrl}?${queryString}`;

  try {
    // Upload the chunk
    console.log(`Uploading chunk ${chunkNumber + 1}/${totalChunks} (${(chunk.size / (1024 * 1024)).toFixed(2)}MB)`);

    try {
      // First try direct upload
      await axios.put(uploadUrl, chunk, {
        headers: {
          'Content-Type': 'application/octet-stream',
          'Content-Length': chunk.size.toString(),
          // Add CORS headers
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'PUT',
        },
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        timeout: 300000, // 5 minutes timeout per chunk
      });

      console.log(`Chunk ${chunkNumber + 1}/${totalChunks} uploaded successfully via direct upload`);
      onComplete();
    } catch (directError: any) {
      // Check if this is a CORS error
      const isCorsError =
        directError.message.includes('CORS') ||
        directError.message.includes('Network Error') ||
        directError.message.includes('Failed to fetch') ||
        directError.message.includes('cross-origin');

      if (isCorsError) {
        console.log(`CORS error detected for chunk ${chunkNumber + 1}. Falling back to proxy upload.`);

        // Use the proxy API instead
        const proxyUrl = `/api/upload-proxy?url=${encodeURIComponent(uploadUrl)}`;

        // Create a FormData object to send the chunk
        const formData = new FormData();
        formData.append('file', chunk);

        // Send the chunk to our proxy API
        await axios.post(proxyUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
          timeout: 600000, // 10 minutes timeout for proxy upload
        });

        console.log(`Chunk ${chunkNumber + 1}/${totalChunks} uploaded successfully via proxy`);
        onComplete();
      } else {
        // Not a CORS error, rethrow
        throw directError;
      }
    }
  } catch (error: any) {
    console.error(`Error uploading chunk ${chunkNumber + 1}/${totalChunks}:`, error.message);
    throw error;
  }
}
