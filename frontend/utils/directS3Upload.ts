import axios from 'axios';
import { multipartUpload } from './s3MultipartUpload';

// Size threshold for using multipart upload (optimized for all file sizes)
// For small files (< 10MB), we'll use direct upload
// For medium and large files (≥ 10MB), we'll use multipart upload
const MULTIPART_THRESHOLD = 10 * 1024 * 1024; // 10MB threshold

/**
 * Uploads a file directly to S3 using the most efficient method based on file size
 * @param url The presigned S3 URL
 * @param file The file to upload
 * @param onProgress Optional callback for progress updates
 */
export async function uploadToS3(
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<void> {
  console.log(`Starting S3 upload for file: ${file.name}, Size: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);

  try {
    // For large files, use multipart upload
    if (file.size > MULTIPART_THRESHOLD) {
      console.log(`File size exceeds ${MULTIPART_THRESHOLD / (1024 * 1024)}MB, using multipart upload`);

      await multipartUpload(file, url, (progress) => {
        if (onProgress) {
          onProgress(progress.percentage);
        }

        // Log detailed progress information
        const speedMBps = (progress.speed / (1024 * 1024)).toFixed(2);
        const uploadedMB = (progress.uploadedBytes / (1024 * 1024)).toFixed(2);
        const totalMB = (progress.totalBytes / (1024 * 1024)).toFixed(2);

        console.log(`Upload progress: ${progress.percentage}% (${uploadedMB}MB / ${totalMB}MB) at ${speedMBps}MB/s`);
      });
    } else {
      // For smaller files, use optimized direct upload
      console.log(`File size is under ${MULTIPART_THRESHOLD / (1024 * 1024)}MB, using optimized direct upload`);

      const startTime = Date.now();

      try {
        // First try direct upload
        // Create optimized axios instance for small file uploads
        const optimizedAxios = axios.create({
          timeout: 120000, // 2 minutes timeout for small files
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
          decompress: false, // Disable decompression for binary data
          headers: {
            'Content-Type': file.type,
            'Content-Length': file.size.toString(),
            'Cache-Control': 'no-cache', // Prevent caching
            'Pragma': 'no-cache',
            // Add CORS headers
            'Origin': window.location.origin,
            'Access-Control-Request-Method': 'PUT',
          }
        });

        // Set performance optimizations
        optimizedAxios.defaults.transitional = {
          clarifyTimeoutError: true,
          forcedJSONParsing: false,
          silentJSONParsing: false
        };

        // Use a buffer for better performance with binary data
        const fileBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(fileBuffer);

        await optimizedAxios.put(url, buffer, {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total && onProgress) {
              const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress(percentage);

              // Calculate and log upload speed
              const elapsedSeconds = (Date.now() - startTime) / 1000;
              if (elapsedSeconds > 0) {
                const speedMBps = ((progressEvent.loaded / elapsedSeconds) / (1024 * 1024)).toFixed(2);
                console.log(`Upload progress: ${percentage}% at ${speedMBps}MB/s`);
              }
            }
          }
        });

        const endTime = Date.now();
        const elapsedSeconds = (endTime - startTime) / 1000;
        const uploadSpeed = (file.size / elapsedSeconds / (1024 * 1024)).toFixed(2);
        console.log(`Small file upload completed in ${elapsedSeconds.toFixed(2)}s at ${uploadSpeed}MB/s via direct upload`);
      } catch (directError: any) {
        // Check if this is a CORS error
        const isCorsError =
          directError.message.includes('CORS') ||
          directError.message.includes('Network Error') ||
          directError.message.includes('Failed to fetch') ||
          directError.message.includes('cross-origin');

        if (isCorsError) {
          console.log(`CORS error detected for small file upload. Falling back to proxy upload.`);

          // Use the proxy API instead
          const proxyUrl = `/api/upload-proxy?url=${encodeURIComponent(url)}`;

          // Create a FormData object to send the file
          const formData = new FormData();
          formData.append('file', file);

          // Send the file to our proxy API
          await axios.post(proxyUrl, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total && onProgress) {
                const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                onProgress(percentage);

                // Calculate and log upload speed
                const elapsedSeconds = (Date.now() - startTime) / 1000;
                if (elapsedSeconds > 0) {
                  const speedMBps = ((progressEvent.loaded / elapsedSeconds) / (1024 * 1024)).toFixed(2);
                  console.log(`Upload progress: ${percentage}% at ${speedMBps}MB/s`);
                }
              }
            },
            maxBodyLength: Infinity,
            maxContentLength: Infinity,
            timeout: 300000, // 5 minutes timeout for proxy upload
          });

          const endTime = Date.now();
          const elapsedSeconds = (endTime - startTime) / 1000;
          const uploadSpeed = (file.size / elapsedSeconds / (1024 * 1024)).toFixed(2);
          console.log(`Small file upload completed in ${elapsedSeconds.toFixed(2)}s at ${uploadSpeed}MB/s via proxy`);
        } else {
          // Not a CORS error, rethrow
          throw directError;
        }
      }
    }

    console.log(`Upload completed successfully for ${file.name}`);

    // Report 100% completion
    if (onProgress) onProgress(100);
  } catch (error: any) {
    console.error('Error in direct S3 upload:', error.message);
    throw error;
  }
}
