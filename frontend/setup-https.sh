#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up HTTPS for Next.js application...${NC}"

# Create certificates directory
mkdir -p certificates
cd certificates

# Check if certificates already exist
if [ -f "server.key" ] && [ -f "server.crt" ]; then
  echo -e "${YELLOW}SSL certificates already exist. Do you want to regenerate them? (y/n)${NC}"
  read -r regenerate
  if [ "$regenerate" != "y" ]; then
    echo -e "${GREEN}Using existing certificates.${NC}"
    cd ..
    exit 0
  fi
fi

echo -e "${YELLOW}Generating self-signed SSL certificates...${NC}"

# Generate SSL certificates
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout server.key -out server.crt -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

if [ $? -eq 0 ]; then
  echo -e "${GREEN}SSL certificates generated successfully!${NC}"
  echo -e "${YELLOW}Note: These are self-signed certificates and will show a security warning in browsers.${NC}"
  echo -e "${YELLOW}For production, use certificates from a trusted certificate authority.${NC}"
else
  echo -e "${RED}Failed to generate SSL certificates.${NC}"
  exit 1
fi

cd ..

# Check if the server.js file exists
if [ ! -f "server.js" ]; then
  echo -e "${RED}server.js file not found. Please make sure it exists.${NC}"
  exit 1
fi

echo -e "${GREEN}HTTPS setup complete!${NC}"
echo -e "${YELLOW}You can now run your Next.js application with HTTPS:${NC}"
echo -e "${YELLOW}npm run dev${NC} - Development mode with HTTPS"
echo -e "${YELLOW}npm run start${NC} - Production mode with HTTPS"
echo -e "${YELLOW}npm run dev:next${NC} - Development mode without HTTPS (original Next.js dev server)"
echo -e "${YELLOW}npm run start:next${NC} - Production mode without HTTPS (original Next.js start command)"
