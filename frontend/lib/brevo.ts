// SMS sending functionality using Brevo API (formerly SendinBlue)
// Using fetch API directly for better TypeScript compatibility

/**
 * Sends an SMS message using Brevo/SendinBlue API
 * @param to Recipient phone number
 * @param message Message content
 * @returns Promise that resolves to true on success
 */
export async function sendSMS(to: string, message: string): Promise<boolean> {
  try {
    // Format the phone number to E.164 format if not already formatted
    let formattedNumber: string = to;
    if (!to.startsWith('+')) {
      // Default to India country code if not specified
      formattedNumber = `+91${to}`;
    }
    
    // Create API payload
    const payload = {
      sender: process.env.BREVO_SENDER || 'Wedzat',
      recipient: formattedNumber,
      content: message,
      type: "transactional"
    };
    
    // Log the request details (redacted for security)
    console.log(`Sending SMS to ${formattedNumber.substring(0, 4)}...${formattedNumber.substring(formattedNumber.length - 4)}`);
    
    // Make API request to Brevo
    const response = await fetch('https://api.brevo.com/v3/transactionalSMS/sms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'api-key': process.env.BREVO_API_KEY || '',
      },
      body: JSON.stringify(payload),
    });
    
    // Parse response
    const data = await response.json();
    
    // Check if the request was successful
    if (!response.ok) {
      throw new Error(`API error: ${response.status} - ${JSON.stringify(data)}`);
    }
    
    console.log(`SMS sent to ${to}, Message ID: ${data.messageId}`);
    return true;
  } catch (error) {
    console.error('Error sending SMS:', error);
    
    // For development, log more detailed error
    if (process.env.NODE_ENV === 'development') {
      if (error instanceof Error) {
        console.log('Error details:', error.message);
      } else {
        console.log('Unknown error type:', error);
      }
      
      // For development, check if API key is configured
      if (!process.env.BREVO_API_KEY) {
        console.error('BREVO_API_KEY is not configured in environment variables');
      }
    }
    
    throw error;
  }
}

/**
 * Sends an OTP verification SMS
 * @param phone Recipient's phone number
 * @param otp The OTP to send
 * @returns Promise that resolves to true on success
 */
export async function sendOtpSMS(phone: string, otp: string): Promise<boolean> {
  const message = `Your Wedzat verification code is: ${otp}. This code will expire in 10 minutes.`;
  return sendSMS(phone, message);
}