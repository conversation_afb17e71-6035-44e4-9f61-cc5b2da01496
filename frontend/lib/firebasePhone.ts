// frontend/lib/firebasePhone.ts
import { PhoneAuthProvider, RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { auth } from './firebase';

// Store confirmation result globally for verification
let confirmationResult: any = null;

/**
 * Sets up reCAPTCHA verifier
 * @param containerId Element ID where reCAPTCHA will be rendered
 * @returns RecaptchaVerifier instance
 */
export function setupRecaptcha(containerId: string) {
  try {
    // Remove any existing reCAPTCHA if present
    const existingRecaptcha = document.querySelector(`#${containerId} div`);
    if (existingRecaptcha) {
      existingRecaptcha.remove();
    }

    // Create new reCAPTCHA verifier
    const recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
      size: 'invisible',
      callback: () => {
        console.log('reCAPTCHA solved');
      },
      'expired-callback': () => {
        console.log('reCAPTCHA expired');
      }
    });

    return recaptchaVerifier;
  } catch (error) {
    console.error('Error setting up reCAPTCHA:', error);
    throw error;
  }
}

/**
 * Sends OTP via Firebase to the provided phone number
 * @param phoneNumber Phone number in E.164 format
 * @param recaptchaVerifier RecaptchaVerifier instance
 * @returns Promise that resolves when SMS is sent
 */
export async function sendOTPWithFirebase(phoneNumber: string, recaptchaVerifier: RecaptchaVerifier) {
  try {
    // Format the phone number to E.164 format if not already formatted
    let formattedNumber: string = phoneNumber;
    if (!phoneNumber.startsWith('+')) {
      // Default to India country code if not specified
      formattedNumber = `+91${phoneNumber}`;
    }
    
    console.log(`Sending Firebase SMS verification to ${formattedNumber}`);
    
    // Send verification code
    confirmationResult = await signInWithPhoneNumber(auth, formattedNumber, recaptchaVerifier);
    
    console.log('SMS verification sent successfully');
    return true;
  } catch (error) {
    console.error('Error sending Firebase SMS verification:', error);
    throw error;
  }
}

/**
 * Verifies the OTP entered by the user
 * @param otp The OTP entered by the user
 * @returns Promise that resolves with the verified phone number
 */
export async function verifyOTPWithFirebase(otp: string) {
  try {
    if (!confirmationResult) {
      throw new Error('No verification in progress');
    }
    
    // Verify the OTP
    const result = await confirmationResult.confirm(otp);
    const user = result.user;
    
    // Get the verified phone number
    const phoneNumber = user.phoneNumber;
    
    // Sign out the user since we're just using Firebase for verification
    await auth.signOut();
    
    return { success: true, phoneNumber };
  } catch (error) {
    console.error('Error verifying OTP:', error);
    throw error;
  }
}