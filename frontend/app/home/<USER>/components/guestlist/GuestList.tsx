"use client";
import React, { useState, useEffect } from "react";
import { Users, UserPlus, Clock, Check, X, Search, Download, Printer, Plus } from "lucide-react";
import axios from "axios";
import { getAuthToken } from "../../utils";

// Import view components
import GroupsView from './GroupsView';
import AttendanceView from './AttendanceView';
import MenusView from './MenusView';

// Types
export interface Guest {
  guest_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  group_id: string;
  group_name?: string;
  menu_id: string;
  menu_name?: string;
  attendance_status: 'attending' | 'pending' | 'declined';
  invitation_sent: boolean;
  invitation_sent_date?: string;
  response_date?: string;
  notes?: string;
}

export interface Group {
  group_id: string;
  name: string;
  description?: string;
  guest_count?: number;
}

export interface MenuOption {
  menu_id: string;
  name: string;
  description?: string;
  is_default: boolean;
}

interface GuestListProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

type TabType = 'groups' | 'attendance' | 'menus';

const GuestList: React.FC<GuestListProps> = ({
  setError: setParentError,
  setSuccessMessage: setParentSuccessMessage,
  setLoading: setParentLoading
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('groups');
  const [groups, setGroups] = useState<Group[]>([]);
  const [guests, setGuests] = useState<Guest[]>([]);
  const [menuOptions, setMenuOptions] = useState<MenuOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Update parent state when local state changes
  useEffect(() => {
    setParentLoading(loading);
  }, [loading, setParentLoading]);

  useEffect(() => {
    setParentError(error);
  }, [error, setParentError]);

  // Add effect to log guests whenever they change
  useEffect(() => {
    console.log('GUESTS STATE CHANGED - Current guests in state:', guests);
  }, [guests]);

  // Add effect to log groups whenever they change
  useEffect(() => {
    console.log('GROUPS STATE CHANGED - Current groups in state:', groups);
  }, [groups]);

  // Add effect to log menu options whenever they change
  useEffect(() => {
    console.log('MENU OPTIONS STATE CHANGED - Current menu options in state:', menuOptions);
  }, [menuOptions]);

  useEffect(() => {
    setParentSuccessMessage(successMessage);
  }, [successMessage, setParentSuccessMessage]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddGuestModal, setShowAddGuestModal] = useState(false);
  const [showAddGroupModal, setShowAddGroupModal] = useState(false);
  // Removed couple names and first-time setup state

  // Stats
  const [stats, setStats] = useState({
    totalGuests: 0,
    attending: 0,
    pending: 0,
    declined: 0,
    adults: 0,
    children: 0
  });

  // Fetch data
  // Make fetchData return a Promise that resolves when all data is fetched
  const fetchData = async () => {
    return new Promise<void>(async (resolve) => {
    setLoading(true);
    setError(null);

    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setLoading(false);
        return;
      }

      console.log('Fetching guest list data...');

      // Fetch guests first to ensure we have the latest data
      const guestsResponse = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-list',
        {
          headers: { Authorization: `Bearer ${token}` },
          // Add cache-busting parameter to prevent caching
          params: { _t: new Date().getTime() }
        }
      );

      const fetchedGuests = guestsResponse.data.guests || [];
      console.log('Fetched guests:', fetchedGuests);

      // Force a deep copy to ensure React detects the state change
      const guestsCopy = JSON.parse(JSON.stringify(fetchedGuests));
      setGuests(guestsCopy);

      // Fetch groups with retry logic for new users
      let fetchedGroups = [];
      try {
        const groupsResponse = await axios.get(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-groups',
          {
            headers: { Authorization: `Bearer ${token}` },
            params: { _t: new Date().getTime() }
          }
        );
        fetchedGroups = groupsResponse.data.groups || [];
      } catch (groupError: any) {
        console.warn('Error fetching groups on first attempt:', groupError);

        // If we get the specific "'id'" error for new users, wait and retry
        if (groupError.response?.data?.error === "'id'") {
          console.log('Detected new user, waiting 1 second and retrying...');

          // Wait 1 second before retrying
          await new Promise(r => setTimeout(r, 1000));

          try {
            // Retry the request
            const retryResponse = await axios.get(
              'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-groups',
              {
                headers: { Authorization: `Bearer ${token}` },
                params: { _t: new Date().getTime() }
              }
            );
            fetchedGroups = retryResponse.data.groups || [];
            console.log('Retry successful, fetched groups:', fetchedGroups);
          } catch (retryError: any) {
            console.error('Error on retry attempt:', retryError);
            // Continue with empty groups, will be handled by backend on next request
          }
        }
      }

      console.log('Fetched groups:', fetchedGroups);

      // Force a deep copy to ensure React detects the state change
      const groupsCopy = JSON.parse(JSON.stringify(fetchedGroups));
      setGroups(groupsCopy);

      // Fetch menu options
      const menuResponse = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/menu-options',
        {
          headers: { Authorization: `Bearer ${token}` },
          // Add cache-busting parameter to prevent caching
          params: { _t: new Date().getTime() }
        }
      );

      const fetchedMenuOptions = menuResponse.data.menu_options || [];
      console.log('Fetched menu options:', fetchedMenuOptions);

      // Force a deep copy to ensure React detects the state change
      const menuOptionsCopy = JSON.parse(JSON.stringify(fetchedMenuOptions));
      setMenuOptions(menuOptionsCopy);

      // Calculate stats
      calculateStats(guestsCopy);

    } catch (err: any) {
      console.error('Error fetching guest list data:', err);
      setError(err.response?.data?.error || 'Failed to load guest list data');
      // Still resolve the promise even if there's an error, so the UI can update
    } finally {
      setLoading(false);
      resolve();
    }
    });
  };

  // Calculate statistics
  const calculateStats = (guestList: Guest[]) => {
    const stats = {
      totalGuests: guestList.length,
      attending: guestList.filter(g => String(g.attendance_status) === 'attending').length,
      pending: guestList.filter(g => String(g.attendance_status) === 'pending').length,
      declined: guestList.filter(g => String(g.attendance_status) === 'declined').length,
      adults: guestList.filter(g => g.menu_name?.toLowerCase().includes('adult')).length,
      children: guestList.filter(g => g.menu_name?.toLowerCase().includes('children')).length
    };

    console.log('Calculated stats:', stats);
    setStats(stats);
  };

  // Debug function removed

  // First-time setup is now handled automatically by the backend

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Filter guests based on search query
  const filteredGuests = guests.filter(guest => {
    const fullName = `${guest.first_name} ${guest.last_name}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase()) ||
           (guest.email && guest.email.toLowerCase().includes(searchQuery.toLowerCase()));
  });

  // Show loading indicator while fetching initial data
  if (loading && groups.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6 max-w-md mx-auto mt-10">
        <div className="flex flex-col items-center justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#B31B1E]"></div>
          <p className="mt-4 text-gray-600">Loading guest list...</p>
        </div>
      </div>
    );
  }

  // Import components dynamically based on active tab
  const renderTabContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#B31B1E]"></div>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-center p-4">{error}</div>;
    }

    switch (activeTab) {
      case 'groups':
        return (
          <div className="p-4">
            <GroupsView
              groups={groups}
              guests={guests}
              setGroups={setGroups}
              setGuests={setGuests}
              fetchData={fetchData}
              menuOptions={menuOptions}
              setError={setError}
              setSuccessMessage={setSuccessMessage}
              showAddGroupModal={showAddGroupModal}
              setShowAddGroupModal={setShowAddGroupModal}
              showAddGuestModal={showAddGuestModal}
              setShowAddGuestModal={setShowAddGuestModal}
            />
          </div>
        );
      case 'attendance':
        return (
          <div className="p-4">
            <AttendanceView
              guests={filteredGuests}
              groups={groups}
              menuOptions={menuOptions}
              setGuests={setGuests}
              fetchData={fetchData}
              setError={setError}
              setSuccessMessage={setSuccessMessage}
            />
          </div>
        );
      case 'menus':
        return (
          <div className="p-4">
            <MenusView
              guests={filteredGuests}
              menuOptions={menuOptions}
              setMenuOptions={setMenuOptions}
              setGuests={setGuests}
              fetchData={fetchData}
              setError={setError}
              setSuccessMessage={setSuccessMessage}
            />
          </div>
        );
      default:
        return null;
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-8 flex justify-center items-center min-h-[300px]">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-red-700 mb-4"></div>
          <p className="text-gray-600">Loading guest list...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Stats Bar */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <div className="bg-red-100 rounded-full p-2 mr-2">
              <Users size={20} className="text-[#B31B1E]" />
            </div>
            <div>
              <div className="text-sm text-gray-500">Guests</div>
              <div className="font-semibold text-black">{stats.totalGuests} Total</div>
            </div>
          </div>

          <div className="flex items-center">
            <div className="bg-red-100 rounded-full p-2 mr-2">
              <UserPlus size={20} className="text-[#B31B1E]" />
            </div>
            <div>
              <div className="text-sm text-gray-500">Menu</div>
              <div className="font-semibold text-black">{stats.adults} Adults, {stats.children} Children</div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <Check size={16} className="text-green-500 mr-1" />
              <span className="text-black">{stats.attending}</span>
            </div>
            <div className="flex items-center">
              <Clock size={16} className="text-amber-500 mr-1" />
              <span className="text-black">{stats.pending}</span>
            </div>
            <div className="flex items-center">
              <X size={16} className="text-red-500 mr-1" />
              <span className="text-black">{stats.declined}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between p-4">
        <div className="flex space-x-2">
          <button
            onClick={() => setShowAddGuestModal(true)}
            className="flex items-center bg-[#B31B1E] text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
          >
            <Plus size={16} className="mr-1" />
            Guest
          </button>

          <button
            onClick={() => setShowAddGroupModal(true)}
            className="flex items-center border border-[#B31B1E] text-[#B31B1E] px-4 py-2 rounded hover:bg-red-50 transition-colors"
          >
            <Plus size={16} className="mr-1" />
            Group
          </button>

          {/* Removed redundant buttons */}
        </div>

        <div className="flex space-x-2">
          <button className="flex items-center border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-50 transition-colors">
            <Download size={16} className="mr-1" />
            Download
          </button>

          <button className="flex items-center border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-50 transition-colors">
            <Printer size={16} className="mr-1" />
            Print
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex">
          <button
            className={`px-6 py-3 font-medium ${activeTab === 'groups' ? 'border-b-2 border-[#B31B1E] text-[#B31B1E]' : 'text-gray-500'}`}
            onClick={() => setActiveTab('groups')}
          >
            GROUPS
          </button>
          <button
            className={`px-6 py-3 font-medium ${activeTab === 'attendance' ? 'border-b-2 border-[#B31B1E] text-[#B31B1E]' : 'text-gray-500'}`}
            onClick={() => setActiveTab('attendance')}
          >
            ATTENDANCE
          </button>
          <button
            className={`px-6 py-3 font-medium ${activeTab === 'menus' ? 'border-b-2 border-[#B31B1E] text-[#B31B1E]' : 'text-gray-500'}`}
            onClick={() => setActiveTab('menus')}
          >
            MENUS
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="p-4 border-b">
        <div className="relative">
          <input
            type="text"
            placeholder="Search guests..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>
      </div>

      {/* Tab Content */}
      {renderTabContent()}

      {/* Error and Success Messages */}
      {error && (
        <div className="mt-4 p-2 bg-red-100 text-red-700 rounded-md mx-4 mb-4">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="mt-4 p-2 bg-green-100 text-green-700 rounded-md mx-4 mb-4">
          {successMessage}
        </div>
      )}
    </div>
  );
};



export default GuestList;
