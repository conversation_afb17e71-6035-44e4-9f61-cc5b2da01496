"use client";
import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import axios from 'axios';
import { getAuthToken } from '../../utils';
import { Group } from './GuestList';
import Image from "next/image";

interface AddGroupModalProps {
  onClose: () => void;
  onSave: () => void;
  group: Group | null;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
}

const AddGroupModal: React.FC<AddGroupModalProps> = ({
  onClose,
  onSave,
  group,
  setError,
  setSuccessMessage
}) => {

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  // Initialize form with group data if editing
  useEffect(() => {
    if (group) {
      setName(group.name || '');
      setDescription(group.description || '');
    }
  }, [group]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name) {
      setError('Group name is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setLoading(false);
        return;
      }

      if (group) {
        // Update existing group
        await axios.put(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest-group',
          {
            group_id: group.group_id,
            name,
            description
          },
          { headers: { Authorization: `Bearer ${token}` } }
        );

        setSuccessMessage('Group updated successfully');
      } else {
        // Add new group
        await axios.post(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-guest-group',
          {
            name,
            description
          },
          { headers: { Authorization: `Bearer ${token}` } }
        );

        setSuccessMessage('Group added successfully');
      }

      onSave();
      onClose();

    } catch (err: any) {
      console.error('Error saving group:', err);
      setError(err.response?.data?.error || 'Failed to save group');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
        style={{
          background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
        }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
        >
          <X size={20} />
        </button>

        {/* Logo */}
        <div className="flex justify-center pt-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        <div className="px-6 py-4">
          <h3 className="text-2xl font-bold mb-2 text-center" style={{ color: "#B31B1E" }}>
            {group ? 'Edit Group' : 'Add Group'}
          </h3>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Group Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Group Name *
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                required
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                rows={3}
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#B31B1E] text-white rounded hover:bg-red-700 disabled:bg-red-300"
              disabled={loading}
            >
              {loading ? 'Saving...' : (group ? 'Update' : 'Add')}
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default AddGroupModal;
