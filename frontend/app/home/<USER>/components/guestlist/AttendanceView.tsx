"use client";
import React, { useState } from 'react';
import { Check, Clock, X, Edit, Trash2, Mail } from 'lucide-react';
import axios from 'axios';
import { getAuthToken } from '../../utils';
import { Guest, Group, MenuOption } from './GuestList';
import AddGuestModal from './AddGuestModal';

interface AttendanceViewProps {
  guests: Guest[];
  groups: Group[];
  menuOptions: MenuOption[];
  setGuests: React.Dispatch<React.SetStateAction<Guest[]>>;
  fetchData: () => Promise<void>;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
}

const AttendanceView: React.FC<AttendanceViewProps> = ({
  guests,
  groups,
  menuOptions,
  setGuests,
  fetchData,
  setError,
  setSuccessMessage
}) => {

  const [selectedGuest, setSelectedGuest] = useState<Guest | null>(null);
  const [showAddGuestModal, setShowAddGuestModal] = useState(false);
  const [showDeleteGuestConfirm, setShowDeleteGuestConfirm] = useState(false);

  // Group guests by attendance status - force string comparison
  const attending = guests.filter(guest => String(guest.attendance_status) === 'attending');
  const pending = guests.filter(guest => String(guest.attendance_status) === 'pending');
  const declined = guests.filter(guest => String(guest.attendance_status) === 'declined');

  // Debug logs
  console.log('All guests:', guests);
  console.log('Attending guests:', attending);
  console.log('Pending guests:', pending);
  console.log('Declined guests:', declined);

  // Check for any guests with unexpected attendance_status values
  const unexpectedStatus = guests.filter(guest =>
    !['attending', 'pending', 'declined'].includes(String(guest.attendance_status)));
  if (unexpectedStatus.length > 0) {
    console.log('Guests with unexpected attendance status:', unexpectedStatus);
  }

  // Update guest attendance status
  const updateGuestAttendance = async (guest: Guest, newStatus: 'attending' | 'pending' | 'declined') => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      await axios.put(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',
        {
          guest_id: guest.guest_id,
          attendance_status: newStatus
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      setGuests(prevGuests =>
        prevGuests.map(g =>
          g.guest_id === guest.guest_id
            ? { ...g, attendance_status: newStatus }
            : g
        )
      );

    } catch (err: any) {
      console.error('Error updating guest attendance:', err);
      setError(err.response?.data?.error || 'Failed to update guest attendance');
    }
  };

  // Update guest menu option
  const updateGuestMenu = async (guest: Guest, menuId: string) => {
    try {
      const token = getAuthToken();
      const selectedMenu = menuOptions.find(m => m.menu_id === menuId);

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      if (!selectedMenu) {
        setError('Menu option not found');
        return;
      }

      await axios.put(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',
        {
          guest_id: guest.guest_id,
          menu_id: menuId
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      setGuests(prevGuests =>
        prevGuests.map(g =>
          g.guest_id === guest.guest_id
            ? { ...g, menu_id: menuId, menu_name: selectedMenu.name }
            : g
        )
      );

    } catch (err: any) {
      console.error('Error updating guest menu:', err);
      setError(err.response?.data?.error || 'Failed to update guest menu');
    }
  };

  // Delete a guest
  const handleDeleteGuest = async (guestId: string) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-guest', {
        headers: { Authorization: `Bearer ${token}` },
        data: { guest_id: guestId }
      });

      setGuests(prevGuests => prevGuests.filter(g => g.guest_id !== guestId));
      setSuccessMessage('Guest deleted successfully');
      setShowDeleteGuestConfirm(false);

    } catch (err: any) {
      console.error('Error deleting guest:', err);
      setError(err.response?.data?.error || 'Failed to delete guest');
    }
  };

  // Send invitation to a guest
  const sendInvitation = async (guest: Guest) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      // Get the first website (in a real app, you'd let the user choose which website to send)
      const websitesResponse = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {
        headers: { Authorization: `Bearer ${token}` }
      });

      const websites = websitesResponse.data.websites || [];

      if (websites.length === 0) {
        setError('You need to create a wedding website first to send invitations');
        return;
      }

      const websiteId = websites[0].website_id;

      await axios.post(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/send-invitation',
        {
          guest_ids: [guest.guest_id],
          website_id: websiteId
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      setSuccessMessage(`Invitation sent to ${guest.first_name} ${guest.last_name}`);
      fetchData(); // Refresh to update invitation status

    } catch (err: any) {
      console.error('Error sending invitation:', err);
      setError(err.response?.data?.error || 'Failed to send invitation');
    }
  };

  // Render a guest list section
  const renderGuestSection = (title: string, icon: React.ReactNode, guestList: Guest[], count: number) => (
    <div className="mb-8">
      <div className="flex items-center mb-4">
        <div className="flex items-center">
          {icon}
          <h3 className="font-semibold text-lg ml-2 text-black">{title}</h3>
          <span className="ml-2 text-gray-500 text-sm">{count}</span>
        </div>
      </div>

      {guestList.length > 0 ? (
        <div className="space-y-2">
          {guestList.map(guest => (
            <div key={guest.guest_id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
              <div className="flex-1 text-black">
                {guest.first_name} {guest.last_name}
              </div>

              {/* Group */}
              <div className="flex-1 text-gray-600 text-sm">
                {guest.group_name}
              </div>

              {/* Menu dropdown */}
              <div className="flex items-center mx-2">
                <div className="relative group">
                  <button className="flex items-center space-x-1 p-1 rounded hover:bg-gray-100">
                    <span className="text-sm text-gray-700">{guest.menu_name || 'Select menu'}</span>
                  </button>
                  <div className="absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block">
                    {menuOptions.map(menu => (
                      <button
                        key={menu.menu_id}
                        onClick={() => updateGuestMenu(guest, menu.menu_id)}
                        className="w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                      >
                        {menu.name}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Attendance dropdown */}
              <div className="flex items-center mx-2">
                <div className="relative group">
                  <button className="flex items-center space-x-1 p-1 rounded hover:bg-gray-100">
                    <span className="text-sm text-gray-700 capitalize">{guest.attendance_status}</span>
                  </button>
                  <div className="absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block">
                    <button
                      onClick={() => updateGuestAttendance(guest, 'attending')}
                      className="flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                    >
                      <Check size={16} className="text-green-500 mr-2" />
                      <span>Attending</span>
                    </button>
                    <button
                      onClick={() => updateGuestAttendance(guest, 'pending')}
                      className="flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                    >
                      <Clock size={16} className="text-amber-500 mr-2" />
                      <span>Pending</span>
                    </button>
                    <button
                      onClick={() => updateGuestAttendance(guest, 'declined')}
                      className="flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                    >
                      <X size={16} className="text-red-500 mr-2" />
                      <span>Declined</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center">
                {guest.email && !guest.invitation_sent && (
                  <button
                    onClick={() => sendInvitation(guest)}
                    className="text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded"
                    title="Send invitation"
                  >
                    <Mail size={16} />
                  </button>
                )}
                <button
                  onClick={() => {
                    setSelectedGuest(guest);
                    setShowAddGuestModal(true);
                  }}
                  className="text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded"
                  title="Edit guest"
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => {
                    setSelectedGuest(guest);
                    setShowDeleteGuestConfirm(true);
                  }}
                  className="text-gray-500 p-1 hover:bg-gray-100 rounded"
                  title="Delete guest"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-gray-500 italic p-2">No guests</div>
      )}
    </div>
  );

  return (
    <div>
      {renderGuestSection(
        'Attending',
        <Check size={20} className="text-green-500" />,
        attending,
        attending.length
      )}

      {renderGuestSection(
        'Pending',
        <Clock size={20} className="text-amber-500" />,
        pending,
        pending.length
      )}

      {renderGuestSection(
        'Declined',
        <X size={20} className="text-red-500" />,
        declined,
        declined.length
      )}

      {/* Delete Guest Confirmation Modal */}
      {showDeleteGuestConfirm && selectedGuest && (
        <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
          <div
            className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
            style={{
              background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            }}
          >
            <h3 className="text-xl font-bold mb-4 text-black">Delete Guest</h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete {selectedGuest.first_name} {selectedGuest.last_name}?
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowDeleteGuestConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteGuest(selectedGuest.guest_id)}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add/Edit Guest Modal */}
      {showAddGuestModal && (
        <AddGuestModal
          groups={groups}
          menuOptions={menuOptions}
          onClose={() => {
            setShowAddGuestModal(false);
            setSelectedGuest(null);
          }}
          onSave={fetchData}
          guest={selectedGuest}
          setError={setError}
          setSuccessMessage={setSuccessMessage}
        />
      )}
    </div>
  );
};

export default AttendanceView;
