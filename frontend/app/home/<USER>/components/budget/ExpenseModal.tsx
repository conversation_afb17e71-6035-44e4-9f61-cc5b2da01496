"use client";
import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { BudgetCategory } from "../../types";
import Image from "next/image";

interface ExpenseModalProps {
  categories: BudgetCategory[];
  selectedCategory: BudgetCategory | null;
  onClose: () => void;
  onSave: (expenseData: {
    name: string;
    category_id: string;
    estimated_budget: number;
    final_cost: number;
    notes: string;
  }) => void;
}

const ExpenseModal: React.FC<ExpenseModalProps> = ({
  categories,
  selectedCategory,
  onClose,
  onSave
}) => {
  const [expenseData, setExpenseData] = useState({
    name: "",
    category_id: selectedCategory ? selectedCategory.category_id : "",
    estimated_budget: "0",
    final_cost: "0",
    notes: ""
  });

  // Update category_id when selectedCategory changes
  useEffect(() => {
    if (selectedCategory) {
      setExpenseData(prev => ({
        ...prev,
        category_id: selectedCategory.category_id
      }));
    }
  }, [selectedCategory]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setExpenseData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      name: expenseData.name,
      category_id: expenseData.category_id,
      estimated_budget: parseFloat(expenseData.estimated_budget) || 0,
      final_cost: parseFloat(expenseData.final_cost) || 0,
      notes: expenseData.notes
    });
  };

  return (
    <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
        style={{
          background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
        }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
        >
          <X size={20} />
        </button>

        {/* Logo */}
        <div className="flex justify-center pt-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        <div className="px-6 py-4">
          <h3 className="text-2xl font-bold mb-2 text-center" style={{ color: "#B31B1E" }}>Add Expense</h3>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
              Expense Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={expenseData.name}
              onChange={handleChange}
              placeholder="Expense name"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="category_id">
              Category
            </label>
            <select
              id="category_id"
              name="category_id"
              value={expenseData.category_id}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
              required
            >
              <option value="" disabled>Select a category</option>
              {categories.map(category => (
                <option key={category.category_id} value={category.category_id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="estimated_budget">
              Estimated Budget
            </label>
            <input
              type="number"
              id="estimated_budget"
              name="estimated_budget"
              value={expenseData.estimated_budget}
              onChange={handleChange}
              placeholder="0"
              min="0"
              step="0.01"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="final_cost">
              Final Cost
            </label>
            <input
              type="number"
              id="final_cost"
              name="final_cost"
              value={expenseData.final_cost}
              onChange={handleChange}
              placeholder="0"
              min="0"
              step="0.01"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="notes">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={expenseData.notes}
              onChange={handleChange}
              placeholder="Add notes..."
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
              rows={3}
            />
          </div>

          <div className="flex justify-center">
            <button
              type="submit"
              className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6"
            >
              Save
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default ExpenseModal;
