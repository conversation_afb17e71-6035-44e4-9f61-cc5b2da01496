"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { DollarSign, Download, Printer, Plus, Edit, Trash, X } from "lucide-react";
import EditableAmount from "./EditableAmount";
import { BudgetCategory, BudgetExpense, BudgetPayment, BudgetTotals } from "../../types";
import { getAuthToken, formatCurrency } from "../../utils";
import CategoryModal from "./CategoryModal";
import ExpenseModal from "./ExpenseModal";
import PaymentModal from "./PaymentModal";
import ExpenseDetails from "./ExpenseDetails";
import PaymentsList from "./PaymentsList";

interface BudgetProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

const Budget: React.FC<BudgetProps> = ({
  setError,
  setSuccessMessage,
  setLoading,
  loading,
  error,
  successMessage
}) => {
  // Budget states
  const [budgetCategories, setBudgetCategories] = useState<BudgetCategory[]>([]);
  const [budgetExpenses, setBudgetExpenses] = useState<BudgetExpense[]>([]);
  const [budgetPayments, setBudgetPayments] = useState<BudgetPayment[]>([]);
  const [budgetTotals, setBudgetTotals] = useState<BudgetTotals>({
    total_estimated: 0,
    total_final: 0,
    total_paid: 0
  });
  const [activeBudgetTab, setActiveBudgetTab] = useState<string>("budget");

  // Modal states
  const [showCategoryModal, setShowCategoryModal] = useState<boolean>(false);
  const [showExpenseModal, setShowExpenseModal] = useState<boolean>(false);
  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false);
  const [showExpenseDetails, setShowExpenseDetails] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<BudgetCategory | null>(null);
  const [selectedExpense, setSelectedExpense] = useState<BudgetExpense | null>(null);

  // Fetch budget data on component mount
  useEffect(() => {
    fetchBudgetData();
  }, []);

  // Budget API functions
  const fetchBudgetData = async () => {
    try {
      setLoading(true);
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setLoading(false);
        return;
      }

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/budget',
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      console.log('API response for budget data:', response.data);

      // Process categories
      if (response.data && response.data.categories && Array.isArray(response.data.categories)) {
        setBudgetCategories(response.data.categories);
      } else {
        setBudgetCategories([]);
      }

      // Process expenses
      if (response.data && response.data.expenses && Array.isArray(response.data.expenses)) {
        setBudgetExpenses(response.data.expenses);

        // Calculate budget totals
        const totals = response.data.expenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {
          acc.total_estimated += expense.estimated_budget || 0;
          acc.total_final += expense.final_cost || 0;
          acc.total_paid += expense.amount_paid || 0;
          return acc;
        }, { total_estimated: 0, total_final: 0, total_paid: 0 });

        setBudgetTotals(totals);
      } else {
        setBudgetExpenses([]);
        setBudgetTotals({ total_estimated: 0, total_final: 0, total_paid: 0 });
      }

      // Process payments
      if (response.data && response.data.payments && Array.isArray(response.data.payments)) {
        setBudgetPayments(response.data.payments);
      } else {
        setBudgetPayments([]);
      }

      setError(null);
    } catch (err: any) {
      console.error('Error fetching budget data:', err);
      setError('Failed to load budget data');
      setBudgetCategories([]);
      setBudgetExpenses([]);
      setBudgetPayments([]);
      setBudgetTotals({ total_estimated: 0, total_final: 0, total_paid: 0 });
    } finally {
      setLoading(false);
    }
  };

  // Delete a category
  // Fix for the deleteCategory and deleteExpense functions



// 1. Modified deleteCategory function
// Delete a category function - adapted from the working checklist example
const deleteCategory = async (categoryId: string) => {
  try {
    setLoading(true);
    const token = getAuthToken();

    if (!token) {
      console.warn('No authentication token found');
      setError('Authentication required');
      return;
    }

    if (!categoryId) {
      console.warn('No category ID provided for deletion');
      setError('Category ID is required for deletion');
      return;
    }

    console.log('Deleting category with ID:', categoryId);

    // Include the data in the request body as the server expects it
    const response = await axios.delete(
      'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-budget-category',
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: { category_id: categoryId } // Send the ID in the request body
      }
    );

    console.log('API response for delete category:', response.data);

    // Check for success using multiple indicators
    if (
      (response.data && response.data.success) || // Standard success format
      (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'
      (response.status >= 200 && response.status < 300) // HTTP success status code
    ) {
      // Remove the category from state
      setBudgetCategories(budgetCategories.filter(cat => cat.category_id !== categoryId));
      // Also remove all expenses associated with this category
      const filteredExpenses = budgetExpenses.filter(exp => exp.category_id !== categoryId);
      setBudgetExpenses(filteredExpenses);

      // Recalculate totals after removing expenses
      const totals = filteredExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {
        acc.total_estimated += expense.estimated_budget || 0;
        acc.total_final += expense.final_cost || 0;
        acc.total_paid += expense.amount_paid || 0;
        return acc;
      }, { total_estimated: 0, total_final: 0, total_paid: 0 });

      setBudgetTotals(totals);
      setError(null); // Clear any previous errors
      setSuccessMessage('Category deleted successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } else {
      console.warn('Unexpected API response format:', response.data);
      setError('Failed to delete category');
    }
  } catch (err: any) {
    console.error('Error deleting category:', err);
    if (err.response && err.response.data && err.response.data.error) {
      setError(`Failed to delete category: ${err.response.data.error}`);
    } else {
      setError('Failed to delete category');
    }
  } finally {
    setLoading(false);
  }
};

// Delete an expense function - adapted from the working checklist example
const deleteExpense = async (expenseId: string) => {
  try {
    setLoading(true);
    const token = getAuthToken();

    if (!token) {
      console.warn('No authentication token found');
      setError('Authentication required');
      return;
    }

    if (!expenseId) {
      console.warn('No expense ID provided for deletion');
      setError('Expense ID is required for deletion');
      return;
    }

    console.log('Deleting expense with ID:', expenseId);

    // Include the data in the request body as the server expects it
    const response = await axios.delete(
      'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-budget-expense',
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: { expense_id: expenseId } // Send the ID in the request body
      }
    );

    console.log('API response for delete expense:', response.data);

    // Check for success using multiple indicators
    if (
      (response.data && response.data.success) || // Standard success format
      (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'
      (response.status >= 200 && response.status < 300) // HTTP success status code
    ) {
      // Remove the expense from state
      const filteredExpenses = budgetExpenses.filter(exp => exp.expense_id !== expenseId);
      setBudgetExpenses(filteredExpenses);

      // Recalculate totals
      const totals = filteredExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {
        acc.total_estimated += expense.estimated_budget || 0;
        acc.total_final += expense.final_cost || 0;
        acc.total_paid += expense.amount_paid || 0;
        return acc;
      }, { total_estimated: 0, total_final: 0, total_paid: 0 });

      setBudgetTotals(totals);
      setError(null); // Clear any previous errors
      setSuccessMessage('Expense deleted successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } else {
      console.warn('Unexpected API response format:', response.data);
      setError('Failed to delete expense');
    }
  } catch (err: any) {
    console.error('Error deleting expense:', err);
    if (err.response && err.response.data && err.response.data.error) {
      setError(`Failed to delete expense: ${err.response.data.error}`);
    } else {
      setError('Failed to delete expense');
    }
  } finally {
    setLoading(false);
  }
};

  // Update an expense - returns true if successful, false otherwise
  const updateExpense = async (expenseId: string, updatedData: Partial<BudgetExpense>): Promise<boolean> => {
    try {
      setLoading(true);
      const token = getAuthToken();

      if (!token) {
        setError('Authentication required');
        return false;
      }

      console.log('Updating expense with ID:', expenseId, 'Data:', updatedData);

      // Use PUT method since CORS is enabled
      const response = await axios({
        method: 'put',
        url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-budget-expense',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: {
          expense_id: expenseId,
          name: updatedData.name,
          estimated_budget: updatedData.estimated_budget,
          final_cost: updatedData.final_cost,
          notes: updatedData.notes
        }
      });

      console.log('Update expense response:', response.data);
      console.log('Response status:', response.status);
      console.log('Response success indicator:', response.data?.success);
      console.log('Response message:', response.data?.message);

      // Check for success using multiple indicators since the API might return different formats
      if (
        (response.data && response.data.success) || // Standard success format
        (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'
        (response.status >= 200 && response.status < 300) // HTTP success status code
      ) {
        // Update the expense in state
        const updatedExpenses = budgetExpenses.map(exp => {
          if (exp.expense_id === expenseId) {
            return { ...exp, ...updatedData };
          }
          return exp;
        });

        setBudgetExpenses(updatedExpenses);

        // Recalculate totals
        const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {
          acc.total_estimated += expense.estimated_budget || 0;
          acc.total_final += expense.final_cost || 0;
          acc.total_paid += expense.amount_paid || 0;
          return acc;
        }, { total_estimated: 0, total_final: 0, total_paid: 0 });

        setBudgetTotals(totals);
        setSuccessMessage('Expense updated successfully');
        setTimeout(() => setSuccessMessage(null), 3000);
        return true;
      } else {
        setError('Failed to update expense in database');
        setTimeout(() => setError(null), 3000);
        return false;
      }
    } catch (err: any) {
      console.error('Error updating expense:', err);
      setError(err.response?.data?.error || 'Failed to update expense');

      // We'll still update the UI optimistically
      const updatedExpenses = budgetExpenses.map(exp => {
        if (exp.expense_id === expenseId) {
          return { ...exp, ...updatedData };
        }
        return exp;
      });

      setBudgetExpenses(updatedExpenses);

      // Recalculate totals
      const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {
        acc.total_estimated += expense.estimated_budget || 0;
        acc.total_final += expense.final_cost || 0;
        acc.total_paid += expense.amount_paid || 0;
        return acc;
      }, { total_estimated: 0, total_final: 0, total_paid: 0 });

      setBudgetTotals(totals);
      return false;
    } finally {
      setLoading(false);
    }
  };



  // Get expenses for a specific category
  const getExpensesByCategory = (categoryId: string) => {
    return budgetExpenses.filter(expense => expense.category_id === categoryId);
  };

  // Calculate total for a category
  const getCategoryTotal = (categoryId: string, field: 'estimated_budget' | 'final_cost' | 'amount_paid') => {
    return getExpensesByCategory(categoryId).reduce((total, expense) => total + (expense[field] || 0), 0);
  };

  // State for selected category to show details
  const [selectedCategoryForDetails, setSelectedCategoryForDetails] = useState<BudgetCategory | null>(null);

  // Function to get category color for chart
  const getCategoryColor = (index: number) => {
    const colors = [
      '#F7A74F', // Orange for Events
      '#4F7BF7', // Blue for Catering
      '#4FE8B7', // Teal for Photography
      '#9D4FF7', // Purple for Planning
      '#F74F4F', // Red for Jewellery
      '#4FC3F7', // Light Blue for Health & Beauty
      '#F7E84F', // Yellow for Entertainment
      '#F74F9D', // Pink for Guests
      '#4FF77B', // Green for Honeymoon
      '#F7874F', // Coral for Transportation
      '#4F8CF7', // Sky Blue for Gifts
      '#C44FF7', // Lavender for Miscellaneous
    ];
    return colors[index % colors.length];
  };

  // State for hover information on pie chart
  const [hoveredCategory, setHoveredCategory] = useState<{name: string, value: number} | null>(null);

  // We're using SVG for the chart, so no need for chart library data structure

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Tabs and Actions */}
      <div className="flex justify-between items-center mb-6">
        <div className="border border-gray-300 rounded-md overflow-hidden">
          <button
            onClick={() => setActiveBudgetTab("budget")}
            className={`px-4 py-2 ${
              activeBudgetTab === "budget"
                ? "bg-gray-200 text-black font-medium"
                : "bg-white text-black"
            }`}
          >
            Budget
          </button>
          <button
            onClick={() => setActiveBudgetTab("payments")}
            className={`px-4 py-2 ${
              activeBudgetTab === "payments"
                ? "bg-gray-200 text-black font-medium"
                : "bg-white text-black"
            }`}
          >
            Payments
          </button>
        </div>

        <div className="flex">
          <button
            className="flex items-center gap-1 text-gray-600 hover:text-gray-800 mr-3"
            onClick={() => console.log('Download budget')}
          >
            <Download size={18} />
            <span>Download</span>
          </button>
          <button
            className="flex items-center gap-1 text-gray-600 hover:text-gray-800"
            onClick={() => window.print()}
          >
            <Printer size={18} />
            <span>Print</span>
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mb-4 p-2 bg-green-100 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}

      {/* Loading state */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#B31B1E]"></div>
        </div>
      ) : (
        <>
          {activeBudgetTab === "budget" ? (
            <div className="flex flex-col md:flex-row gap-6">
              {/* Left Column - Categories List */}
              <div className="w-full md:w-1/3 border border-gray-200 rounded-lg overflow-hidden">
                {/* Add Category Button */}
                <div className="p-3 border-b border-gray-200 bg-gray-50">
                  <button
                    onClick={() => setShowCategoryModal(true)}
                    className="flex items-center gap-2 text-[#B31B1E] hover:text-[#8a1416]"
                  >
                    <Plus size={18} className="text-[#B31B1E]" />
                    <span>New category</span>
                  </button>
                </div>

                {/* Categories List */}
                <div className="divide-y divide-gray-200">
                  {budgetCategories.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No budget categories yet. Add a category to get started!</p>
                  ) : (
                    budgetCategories.map((category) => (
                      <div
                        key={category.category_id}
                        className={`flex justify-between items-center p-4 hover:bg-gray-50 cursor-pointer ${selectedCategoryForDetails?.category_id === category.category_id ? 'border-l-4 border-[#B31B1E]' : ''}`}
                      >
                        <div
                          className="flex items-center gap-3 flex-grow"
                          onClick={() => setSelectedCategoryForDetails(category)}
                        >
                          <span className="text-black">{category.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-black font-medium">{formatCurrency(getCategoryTotal(category.category_id, 'estimated_budget'))}</span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (window.confirm(`Are you sure you want to delete the category "${category.name}"? This will also delete all expenses in this category.`)) {
                                deleteCategory(category.category_id);
                              }
                            }}
                            className="text-gray-500 hover:text-[#B31B1E] ml-2"
                          >
                            <Trash size={16} />
                          </button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Right Column - Budget Details or Category Details */}
              <div className="w-full md:w-2/3">
                {selectedCategoryForDetails ? (
                  /* Category Details View */
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    {/* Category Header */}
                    <div className="flex justify-between items-center p-4 bg-white border-b border-gray-200">
                      <div className="flex items-center gap-2">
                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                          <DollarSign size={20} className="text-gray-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-black">{selectedCategoryForDetails.name}</h3>
                      </div>
                      <button onClick={() => setSelectedCategoryForDetails(null)} className="text-gray-500 hover:text-gray-700">
                        <X size={20} />
                      </button>
                    </div>

                    {/* Category Budget Info */}
                    <div className="p-4 border-b border-gray-200">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-600">Estimated budget:</span>
                        <span className="text-black font-medium">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'estimated_budget'))}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Final Cost:</span>
                        <span className="text-black font-medium">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'final_cost'))}</span>
                      </div>

                      {/* Progress bar */}
                      <div className="mt-3 bg-gray-200 rounded-full h-2.5 overflow-hidden">
                        <div
                          className="bg-green-500 h-2.5"
                          style={{ width: `${Math.min(100, (getCategoryTotal(selectedCategoryForDetails.category_id, 'estimated_budget') / budgetTotals.total_estimated) * 100)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Expenses Table */}
                    <div className="divide-y divide-gray-200">
                      <div className="grid grid-cols-4 p-3 bg-gray-50 text-xs font-medium text-gray-500 uppercase">
                        <div>EXPENSE</div>
                        <div className="text-right">ESTIMATED BUDGET</div>
                        <div className="text-right">FINAL COST</div>
                        <div className="text-right">PAID</div>
                      </div>

                      {getExpensesByCategory(selectedCategoryForDetails.category_id).length === 0 ? (
                        <p className="text-gray-500 text-center py-4">No expenses in this category yet.</p>
                      ) : (
                        getExpensesByCategory(selectedCategoryForDetails.category_id).map((expense) => (
                          <div key={expense.expense_id} className="grid grid-cols-4 p-4 hover:bg-gray-50 relative group">
                            <div className="text-black flex items-center">
                              {expense.name}
                              <button
                                onClick={() => {
                                  if (window.confirm(`Are you sure you want to delete the expense "${expense.name}"?`)) {
                                    deleteExpense(expense.expense_id);
                                  }
                                }}
                                className="text-gray-400 hover:text-[#B31B1E] ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                title="Delete expense"
                              >
                                <Trash size={14} />
                              </button>
                            </div>
                            <div className="text-black text-right">
                              <EditableAmount
                                initialValue={expense.estimated_budget}
                                onSave={async (newValue) => {
                                  if (newValue !== expense.estimated_budget) {
                                    const updatedExpense = {
                                      ...expense,
                                      estimated_budget: newValue,
                                      name: expense.name,
                                      final_cost: expense.final_cost
                                    };
                                    try {
                                      await updateExpense(updatedExpense.expense_id, updatedExpense);
                                      return true; // Success
                                    } catch (error) {
                                      console.error('Error updating estimated budget:', error);
                                      return false; // Failed
                                    }
                                  }
                                  return true; // No change needed
                                }}
                              />
                            </div>
                            <div className="text-black text-right">
                              <EditableAmount
                                initialValue={expense.final_cost}
                                onSave={async (newValue) => {
                                  if (newValue !== expense.final_cost) {
                                    const updatedExpense = {
                                      ...expense,
                                      final_cost: newValue,
                                      name: expense.name,
                                      estimated_budget: expense.estimated_budget
                                    };
                                    try {
                                      await updateExpense(updatedExpense.expense_id, updatedExpense);
                                      return true; // Success
                                    } catch (error) {
                                      console.error('Error updating final cost:', error);
                                      return false; // Failed
                                    }
                                  }
                                  return true; // No change needed
                                }}
                              />
                            </div>
                            <div
                              className="text-black text-right cursor-pointer hover:text-[#B31B1E]"
                              onClick={() => {
                                setSelectedExpense(expense);
                                setShowPaymentModal(true);
                              }}
                              title="Click to add payment"
                            >
                              {formatCurrency(expense.amount_paid)}
                            </div>
                          </div>
                        ))
                      )}

                      {/* Add Expense Button */}
                      <div className="p-4">
                        <button
                          onClick={() => {
                            setSelectedCategory(selectedCategoryForDetails);
                            setShowExpenseModal(true);
                          }}
                          className="flex items-center gap-1 text-[#B31B1E] hover:text-[#8a1416]"
                        >
                          <Plus size={16} />
                          <span>Add new expense</span>
                        </button>
                      </div>

                      {/* Total Row */}
                      <div className="grid grid-cols-4 p-4 bg-gray-50 font-medium">
                        <div className="text-black">Total:</div>
                        <div className="text-black text-right">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'estimated_budget'))}</div>
                        <div className="text-black text-right">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'final_cost'))}</div>
                        <div className="text-black text-right">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'amount_paid'))}</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* Budget Overview */
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="p-6 border-b border-gray-200">
                      <h2 className="text-2xl font-bold text-center text-black mb-6">Budget</h2>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Estimated Budget */}
                        <div className="flex flex-col items-center">
                          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600">
                              <path d="M12.5 16.8c-1.3-1-2.5-2.1-3.5-3.4C8 12 7.5 10.6 7.5 9c0-1.4.5-2.7 1.5-3.6 1-1 2.3-1.4 3.8-1.4 1.5 0 2.8.5 3.8 1.4 1 1 1.5 2.2 1.5 3.6 0 1.6-.5 3-1.5 4.4-1 1.3-2.2 2.4-3.5 3.4h-.6z"/>
                              <path d="M17.5 22h-11c-1.5 0-2.8-.5-3.8-1.5-1-1-1.5-2.3-1.5-3.8 0-1.5.5-2.8 1.5-3.8 1-1 2.3-1.5 3.8-1.5h11c1.5 0 2.8.5 3.8 1.5 1 1 1.5 2.3 1.5 3.8 0 1.5-.5 2.8-1.5 3.8-1 1-2.3 1.5-3.8 1.5z"/>
                            </svg>
                          </div>
                          <h3 className="text-center text-gray-600 uppercase text-sm font-medium">ESTIMATED BUDGET</h3>
                          <p className="text-3xl font-bold text-black mt-1">{formatCurrency(budgetTotals.total_estimated)}</p>
                          <button
                            className="text-[#B31B1E] text-sm mt-2"
                            onClick={() => console.log('Edit budget')}
                          >
                            Edit budget
                          </button>
                        </div>

                        {/* Final Cost */}
                        <div className="flex flex-col items-center">
                          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600">
                              <rect x="2" y="5" width="20" height="14" rx="2"/>
                              <line x1="2" y1="10" x2="22" y2="10"/>
                            </svg>
                          </div>
                          <h3 className="text-center text-gray-600 uppercase text-sm font-medium">FINAL COST</h3>
                          <p className="text-3xl font-bold text-black mt-1">{formatCurrency(budgetTotals.total_final)}</p>
                          <div className="flex text-sm mt-2">
                            <p className="text-gray-600">Paid: <span className="text-green-600">{formatCurrency(budgetTotals.total_paid)}</span></p>
                            <p className="text-gray-600 ml-4">Pending: <span className="text-blue-600">{formatCurrency(budgetTotals.total_final - budgetTotals.total_paid)}</span></p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Expenses Chart */}
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-4 text-black">Expenses</h3>

                      {budgetCategories.length === 0 ? (
                        <p className="text-gray-500 text-center py-4">No budget categories yet. Add a category to get started!</p>
                      ) : (
                        <div>
                          {/* Circular Progress Chart - Representing different categories with their colors */}
                          <div className="flex justify-center mb-6">
                            <div className="w-80 h-80 relative flex items-center justify-center">
                              {/* Hover tooltip */}
                              {hoveredCategory && (
                                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full bg-white p-2 rounded shadow-md z-10 border border-gray-200">
                                  <p className="text-sm font-medium">{hoveredCategory.name}</p>
                                  <p className="text-sm">{formatCurrency(hoveredCategory.value)}</p>
                                </div>
                              )}
                              {/* Create circular progress chart */}
                              <svg viewBox="0 0 200 200" className="w-full h-full">
                                {/* Background circles with increased spacing between them */}
                                <circle cx="100" cy="100" r="80" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="72" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="64" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="56" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="48" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="40" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="32" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="24" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />
                                <circle cx="100" cy="100" r="16" fill="none" stroke="#f0f0f0" strokeWidth="6" strokeLinecap="round" />

                                {/* Monetary value labels positioned in the top-right quadrant */}
                                {(() => {
                                  // Find the maximum budget value for scaling
                                  const sortedCategories = [...budgetCategories].sort((a, b) => {
                                    const aValue = getCategoryTotal(a.category_id, 'estimated_budget');
                                    const bValue = getCategoryTotal(b.category_id, 'estimated_budget');
                                    return bValue - aValue; // Descending order
                                  });

                                  const maxBudgetValue = sortedCategories.length > 0
                                    ? getCategoryTotal(sortedCategories[0].category_id, 'estimated_budget')
                                    : 0;

                                  // Maximum value for the chart (rounded up to nearest 20k)
                                  const chartMaxValue = Math.ceil(maxBudgetValue / 20000) * 20000;

                                  // Generate 6 labels for the top-right quadrant (0 to 180 degrees)
                                  return Array.from({ length: 7 }).map((_, i) => {
                                    // Calculate angle (0 to 180 degrees)
                                    const angle = (i * 30) * Math.PI / 180;

                                    // Calculate value (0 to chartMaxValue)
                                    const value = Math.round((i / 6) * chartMaxValue / 1000);

                                    // Position the label
                                    const x = 100 + 90 * Math.cos(angle - Math.PI/2);
                                    const y = 100 + 90 * Math.sin(angle - Math.PI/2);

                                    // Determine text anchor based on position
                                    let textAnchor = "middle";
                                    if (angle < Math.PI/4) textAnchor = "start";
                                    if (angle > 3*Math.PI/4) textAnchor = "end";

                                    return (
                                      <text
                                        key={i}
                                        x={x}
                                        y={y}
                                        fontSize="8"
                                        fill="#888"
                                        textAnchor={textAnchor}
                                        dominantBaseline="middle"
                                      >
                                        {value > 0 ? `${value}k` : '0'}
                                      </text>
                                    );
                                  });
                                })()}

                                {budgetCategories.length > 0 && (
                                  <>
                                    {/* Calculate and render circular progress arcs */}
                                    {(() => {
                                      // Sort categories by estimated budget (descending) for better visualization
                                      const sortedCategories = [...budgetCategories].sort((a, b) => {
                                        const aValue = getCategoryTotal(a.category_id, 'estimated_budget');
                                        const bValue = getCategoryTotal(b.category_id, 'estimated_budget');
                                        return bValue - aValue; // Descending order
                                      });

                                      // Find the maximum budget value for scaling
                                      const maxBudgetValue = sortedCategories.length > 0
                                        ? getCategoryTotal(sortedCategories[0].category_id, 'estimated_budget')
                                        : 0;

                                      // Maximum value for the chart (rounded up to nearest 20k)
                                      const chartMaxValue = Math.ceil(maxBudgetValue / 20000) * 20000;

                                      return sortedCategories.map((category, index) => {
                                        const categoryValue = getCategoryTotal(category.category_id, 'estimated_budget');
                                        if (categoryValue === 0) return null; // Skip zero-value categories

                                        // Calculate the percentage of the max value
                                        const percentage = categoryValue / chartMaxValue;

                                        // Calculate the angle size based on the percentage (180 degrees is half circle)
                                        // We use 180 degrees to show the arcs in the top-right quadrant
                                        const angleSize = percentage * 180;

                                        // All arcs start from the same angle (0 degrees, which is at the right)
                                        const startAngle = 0; // 0 degrees is at the right (3 o'clock position)

                                        // Calculate radius with gaps between circles
                                        const radius = 80 - (index * 8);
                                        if (radius < 12) return null; // Skip if we run out of space

                                        // Convert angles to radians (subtract 90 to start at the top)
                                        const startRad = (startAngle - 90) * Math.PI / 180;
                                        const endRad = (startAngle + angleSize - 90) * Math.PI / 180;

                                        // Calculate arc points
                                        const x1 = 100 + radius * Math.cos(startRad);
                                        const y1 = 100 + radius * Math.sin(startRad);
                                        const x2 = 100 + radius * Math.cos(endRad);
                                        const y2 = 100 + radius * Math.sin(endRad);

                                        // Determine if the arc should be drawn as a large arc
                                        const largeArcFlag = angleSize > 180 ? 1 : 0;

                                        // Create the SVG path for the arc
                                        const path = [
                                          `M ${x1} ${y1}`,
                                          `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`
                                        ].join(' ');

                                        return (
                                          <path
                                            key={category.category_id}
                                            d={path}
                                            fill="none"
                                            stroke={getCategoryColor(index)}
                                            strokeWidth="6"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            onMouseEnter={() => setHoveredCategory({name: category.name, value: categoryValue})}
                                            onMouseLeave={() => setHoveredCategory(null)}
                                            style={{ cursor: 'pointer' }}
                                          />
                                        );
                                      });
                                    })()}
                                    {/* Center white circle */}
                                    <circle cx="100" cy="100" r="10" fill="white" stroke="#f0f0f0" strokeWidth="1" />
                                  </>
                                )}
                              </svg>
                            </div>
                          </div>

                          {/* Chart Legend - Two columns with category names and values */}
                          <div className="flex justify-between">
                            {/* Left column */}
                            <div className="w-1/2 pr-4">
                              {budgetCategories.slice(0, Math.ceil(budgetCategories.length / 2)).map((category, index) => (
                                <div key={category.category_id} className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-5 rounded-sm"
                                      style={{ backgroundColor: getCategoryColor(index) }}
                                    ></div>
                                    <span className="text-sm font-medium" style={{ color: getCategoryColor(index) }}>{category.name}</span>
                                  </div>
                                  <span className="text-sm font-medium" style={{ color: getCategoryColor(index) }}>
                                    ₹ {getCategoryTotal(category.category_id, 'estimated_budget').toLocaleString()}
                                  </span>
                                </div>
                              ))}
                            </div>

                            {/* Right column */}
                            <div className="w-1/2 pl-4">
                              {budgetCategories.slice(Math.ceil(budgetCategories.length / 2)).map((category, index) => (
                                <div key={category.category_id} className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-5 rounded-sm"
                                      style={{ backgroundColor: getCategoryColor(index + Math.ceil(budgetCategories.length / 2)) }}
                                    ></div>
                                    <span className="text-sm font-medium" style={{ color: getCategoryColor(index + Math.ceil(budgetCategories.length / 2)) }}>
                                      {category.name}
                                    </span>
                                  </div>
                                  <span className="text-sm font-medium" style={{ color: getCategoryColor(index + Math.ceil(budgetCategories.length / 2)) }}>
                                    ₹ {getCategoryTotal(category.category_id, 'estimated_budget').toLocaleString()}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <PaymentsList
              payments={budgetPayments}
              expenses={budgetExpenses}
            />
          )}
        </>
      )}

      {/* Modals */}
      {showCategoryModal && (
        <CategoryModal
          onClose={() => setShowCategoryModal(false)}
          onSave={async (name) => {
            try {
              const token = getAuthToken();

              if (!token) {
                setError('Authentication required');
                return;
              }

              const response = await axios({
                method: 'post',
                url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-budget-item',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                },
                data: { name }
              });

              if (response.data && response.data.category) {
                setBudgetCategories([...budgetCategories, response.data.category]);
                setSuccessMessage('Category added successfully');
                setTimeout(() => setSuccessMessage(null), 3000);
                setShowCategoryModal(false);
              } else {
                setError('Failed to add category');
              }
            } catch (err: any) {
              console.error('Error adding category:', err);
              setError(err.response?.data?.error || 'Failed to add category');
            }
          }}
        />
      )}

      {showExpenseModal && (
        <ExpenseModal
          categories={budgetCategories}
          selectedCategory={selectedCategory}
          onClose={() => {
            setShowExpenseModal(false);
            setSelectedCategory(null);
          }}
          onSave={async (expenseData) => {
            try {
              const token = getAuthToken();

              if (!token) {
                setError('Authentication required');
                return;
              }

              const response = await axios({
                method: 'post',
                url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-budget-expense',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                },
                data: expenseData
              });

              if (response.data && response.data.expense) {
                setBudgetExpenses([...budgetExpenses, response.data.expense]);
                // Recalculate totals
                const updatedExpenses = [...budgetExpenses, response.data.expense];
                const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {
                  acc.total_estimated += expense.estimated_budget || 0;
                  acc.total_final += expense.final_cost || 0;
                  acc.total_paid += expense.amount_paid || 0;
                  return acc;
                }, { total_estimated: 0, total_final: 0, total_paid: 0 });

                setBudgetTotals(totals);
                setSuccessMessage('Expense added successfully');
                setTimeout(() => setSuccessMessage(null), 3000);
                setShowExpenseModal(false);
                setSelectedCategory(null);
              } else {
                setError('Failed to add expense');
              }
            } catch (err: any) {
              console.error('Error adding expense:', err);
              setError(err.response?.data?.error || 'Failed to add expense');
            }
          }}
        />
      )}

      {showPaymentModal && (
        <PaymentModal
          expenses={budgetExpenses}
          selectedExpenseId={selectedExpense?.expense_id}
          onClose={() => setShowPaymentModal(false)}
          onSave={async (paymentData) => {
            try {
              const token = getAuthToken();

              if (!token) {
                setError('Authentication required');
                return;
              }

              const response = await axios({
                method: 'post',
                url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-budget-payment',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                },
                data: paymentData
              });

              if (response.data && response.data.payment) {
                setBudgetPayments([...budgetPayments, response.data.payment]);
                // Update the expense's amount_paid
                const updatedExpenses = budgetExpenses.map(expense => {
                  if (expense.expense_id === paymentData.expense_id) {
                    return {
                      ...expense,
                      amount_paid: (expense.amount_paid || 0) + paymentData.amount
                    };
                  }
                  return expense;
                });

                setBudgetExpenses(updatedExpenses);

                // Recalculate totals
                const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {
                  acc.total_estimated += expense.estimated_budget || 0;
                  acc.total_final += expense.final_cost || 0;
                  acc.total_paid += expense.amount_paid || 0;
                  return acc;
                }, { total_estimated: 0, total_final: 0, total_paid: 0 });

                setBudgetTotals(totals);
                setSuccessMessage('Payment added successfully');
                setTimeout(() => setSuccessMessage(null), 3000);
                setShowPaymentModal(false);
              } else {
                setError('Failed to add payment');
              }
            } catch (err: any) {
              console.error('Error adding payment:', err);
              setError(err.response?.data?.error || 'Failed to add payment');
            }
          }}
        />
      )}

      {showExpenseDetails && selectedExpense && (
        <ExpenseDetails
          expense={selectedExpense}
          payments={budgetPayments.filter(p => p.expense_id === selectedExpense.expense_id)}
          onClose={() => {
            setShowExpenseDetails(false);
            setSelectedExpense(null);
          }}
          onAddPayment={() => {
            // Keep the selected expense when opening the payment modal
            setShowExpenseDetails(false);
            setShowPaymentModal(true);
          }}
          onUpdateExpense={updateExpense}
        />
      )}
    </div>
  );
};

export default Budget;

