"use client";
import React, { useState, useRef, useEffect } from 'react';
import { formatCurrency } from '../../utils';

interface EditableAmountProps {
  initialValue: number;
  onSave: (value: number) => Promise<boolean>;
}

const EditableAmount: React.FC<EditableAmountProps> = ({ initialValue, onSave }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(initialValue);
  const [displayValue, setDisplayValue] = useState(formatCurrency(initialValue));
  const inputRef = useRef<HTMLInputElement>(null);

  // Update display value when initialValue changes
  useEffect(() => {
    setValue(initialValue);
    setDisplayValue(formatCurrency(initialValue));
  }, [initialValue]);

  const handleClick = () => {
    setIsEditing(true);
    // Focus the input after rendering
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.select();
      }
    }, 0);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDisplayValue(e.target.value);
  };

  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(false);

  const handleBlur = async () => {
    // Parse the value (remove currency symbol and commas)
    const numericValue = parseFloat(displayValue.replace(/[^0-9.]/g, ''));

    if (!isNaN(numericValue) && numericValue !== value) {
      setIsSaving(true);
      try {
        const success = await onSave(numericValue);
        if (success) {
          setValue(numericValue);
          setDisplayValue(formatCurrency(numericValue));
          setSaveError(false);
        } else {
          // If save failed, revert to the previous value
          setDisplayValue(formatCurrency(value));
          setSaveError(true);
          // Auto-hide error after 3 seconds
          setTimeout(() => setSaveError(false), 3000);
        }
      } catch (error) {
        console.error('Error saving value:', error);
        setDisplayValue(formatCurrency(value));
        setSaveError(true);
        // Auto-hide error after 3 seconds
        setTimeout(() => setSaveError(false), 3000);
      } finally {
        setIsSaving(false);
        setIsEditing(false);
      }
    } else {
      // If invalid or unchanged, revert to the previous value
      setDisplayValue(formatCurrency(value));
      setIsEditing(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur(); // Trigger the blur event
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setDisplayValue(formatCurrency(value)); // Revert to the previous value
    }
  };

  return (
    <div className="relative">
      {isEditing ? (
        <input
          ref={inputRef}
          type="text"
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className="w-full text-right bg-transparent border-b border-[#B31B1E] focus:outline-none"
          disabled={isSaving}
          autoFocus
        />
      ) : (
        <div
          onClick={handleClick}
          className={`cursor-pointer w-full text-right ${saveError ? 'text-red-500' : 'hover:text-[#B31B1E]'}`}
        >
          {isSaving ? (
            <span className="text-gray-500">Saving...</span>
          ) : (
            formatCurrency(value)
          )}
          {saveError && (
            <span className="text-xs text-red-500 absolute -bottom-5 right-0">Failed to save</span>
          )}
        </div>
      )}
    </div>
  );
};

export default EditableAmount;
