"use client";
import React, { useState } from "react";
import { X, Plus, Edit } from "lucide-react";
import { BudgetExpense, BudgetPayment } from "../../types";
import { formatCurrency, formatDate } from "../../utils";
import Image from "next/image";

interface ExpenseDetailsProps {
  expense: BudgetExpense;
  payments: BudgetPayment[];
  onClose: () => void;
  onAddPayment: () => void;
  onUpdateExpense: (expenseId: string, updatedData: Partial<BudgetExpense>) => void;
}

const ExpenseDetails: React.FC<ExpenseDetailsProps> = ({
  expense,
  payments,
  onClose,
  onAddPayment,
  onUpdateExpense
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editedExpense, setEditedExpense] = useState<{
    name: string;
    estimated_budget: number;
    final_cost: number;
    notes: string;
  }>({
    name: expense.name,
    estimated_budget: expense.estimated_budget,
    final_cost: expense.final_cost,
    notes: expense.notes || ''
  });

  const handleSaveEdit = () => {
    onUpdateExpense(expense.expense_id, editedExpense);
    setIsEditing(false);
  };
  return (
    <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-lg shadow-lg w-full max-w-2xl relative overflow-hidden"
        style={{
          background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div className="flex justify-between items-center p-4">
          <div className="flex items-center">
            <h3 className="text-xl font-semibold text-black">{expense.name}</h3>
            <span className="ml-2 text-gray-500">{expense.category_name}</span>
          </div>
          <div className="flex items-center gap-4">
            <button
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              <Edit size={18} />
            </button>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Logo */}
        <div className="flex justify-center">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        <div className="px-6 py-4">

        {isEditing ? (
          <div className="mb-6">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">NAME</label>
                <input
                  type="text"
                  value={editedExpense.name}
                  onChange={(e) => setEditedExpense({...editedExpense, name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-600 text-sm font-medium mb-2">ESTIMATED BUDGET</label>
                  <input
                    type="number"
                    value={editedExpense.estimated_budget}
                    onChange={(e) => setEditedExpense({...editedExpense, estimated_budget: parseFloat(e.target.value) || 0})}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                  />
                </div>

                <div>
                  <label className="block text-gray-600 text-sm font-medium mb-2">FINAL COST</label>
                  <input
                    type="number"
                    value={editedExpense.final_cost}
                    onChange={(e) => setEditedExpense({...editedExpense, final_cost: parseFloat(e.target.value) || 0})}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                  />
                </div>
              </div>

              <div>
                <label className="block text-gray-600 text-sm font-medium mb-2">NOTES</label>
                <textarea
                  value={editedExpense.notes}
                  onChange={(e) => setEditedExpense({...editedExpense, notes: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                  rows={3}
                />
              </div>

              <div className="flex justify-end gap-2 mt-2">
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEdit}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-gray-600 text-sm font-medium mb-2">ESTIMATED BUDGET</h4>
                <p className="text-2xl font-bold text-black">{formatCurrency(expense.estimated_budget)}</p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-gray-600 text-sm font-medium mb-2">FINAL COST</h4>
                <p className="text-2xl font-bold text-black">{formatCurrency(expense.final_cost)}</p>
                <div className="flex text-sm mt-2">
                  <p className="text-gray-600">Paid: <span className="text-green-600">{formatCurrency(expense.amount_paid)}</span></p>
                  <p className="text-gray-600 ml-4">Pending: <span className="text-blue-600">{formatCurrency(expense.final_cost - expense.amount_paid)}</span></p>
                </div>
              </div>
            </div>

            {expense.notes && (
              <div className="mb-6">
                <h4 className="text-gray-600 text-sm font-medium mb-2">NOTES</h4>
                <p className="text-black">{expense.notes}</p>
              </div>
            )}
          </>
        )}

        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-gray-600 text-sm font-medium">PAYMENTS</h4>
            <button
              onClick={onAddPayment}
              className="flex items-center gap-1 text-purple-600 hover:text-purple-800"
            >
              <Plus size={16} />
              <span>Add payment</span>
            </button>
          </div>

          {payments.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No payments recorded yet.</p>
          ) : (
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Method
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Paid By
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payments.map((payment) => (
                    <tr key={payment.payment_id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(payment.payment_date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(payment.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.payment_method || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.paid_by || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseDetails;
