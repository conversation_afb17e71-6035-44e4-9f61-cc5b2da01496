// Get authentication token
export const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    // Try multiple possible keys
    const token = localStorage.getItem('token') ||
                 localStorage.getItem('jwt_token') ||
                 localStorage.getItem('auth_token') ||
                 localStorage.getItem('wedzat_token');

    // Log token for debugging
    console.log('Auth token retrieved:', token ? `${token.substring(0, 10)}...` : 'null');

    return token;
  }
  return null;
};

// Format currency
export const formatCurrency = (amount: number): string => {
  return `₹ ${amount.toLocaleString('en-IN')}`;
};

// Format date
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};
