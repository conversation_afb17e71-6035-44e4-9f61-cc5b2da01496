"use client";
import React, { useState, useEffect, Suspense } from "react";
import {
  TopNavigation,
  SideNavigation,
} from "../../../components/HomeDashboard/Navigation";
import { useSearchParams, useRouter } from "next/navigation";
import {
  CheckSquare,
  DollarSign,
  Users,
  Globe,
  Image,
} from "lucide-react";

// Import components
import Checklist from "./components/checklist/Checklist";
import Budget from "./components/budget/Budget";
import GuestList from "./components/guestlist/GuestList";
import Vendors from "./components/vendors/Vendors";
import Websites from "./components/websites/Websites";

// Loading fallback component
function TabsLoading() {
  return <div className="text-center py-4">Loading tools...</div>;
}

// Create a client component that safely uses useSearchParams
function TabContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const tabParam = searchParams?.get("tab");
  const [activeTab, setActiveTab] = useState<string>(tabParam || "checklist");

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Update active tab when URL parameter changes
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);



  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    router.push(`/home/<USER>
  };

  return (
    <div className="flex flex-col w-full">
      <h2 className="text-xl font-semibold mb-4 text-black">Planning Tools</h2>

      {/* Tool Icons Row */}
      <div className="grid grid-cols-5 gap-2 mb-6 w-full">
        <div
          onClick={() => handleTabChange("checklist")}
          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === "checklist" ? "bg-gray-100" : "bg-white border border-gray-200"}`}
        >
          <div className="w-10 h-10 flex items-center justify-center mb-2">
            <CheckSquare size={24} className="text-[#B31B1E]" />
          </div>
          <span className="text-sm font-medium text-black">Checklist</span>
        </div>

        <div
          onClick={() => handleTabChange("guestlist")}
          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === "guestlist" ? "bg-gray-100" : "bg-white border border-gray-200"}`}
        >
          <div className="w-10 h-10 flex items-center justify-center mb-2">
            <Users size={24} className="text-[#B31B1E]" />
          </div>
          <span className="text-sm font-medium text-black">Guest list</span>
        </div>

        <div
          onClick={() => handleTabChange("budget")}
          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === "budget" ? "bg-gray-100" : "bg-white border border-gray-200"}`}
        >
          <div className="w-10 h-10 flex items-center justify-center mb-2">
            <DollarSign size={24} className="text-[#B31B1E]" />
          </div>
          <span className="text-sm font-medium text-black">Budget</span>
        </div>

        <div
          onClick={() => handleTabChange("websites")}
          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === "websites" ? "bg-gray-100" : "bg-white border border-gray-200"}`}
        >
          <div className="w-10 h-10 flex items-center justify-center mb-2">
            <Globe size={24} className="text-[#B31B1E]" />
          </div>
          <span className="text-sm font-medium text-black">Websites</span>
        </div>

        <div
          onClick={() => handleTabChange("digialbum")}
          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === "digialbum" ? "bg-gray-100" : "bg-white border border-gray-200"}`}
        >
          <div className="w-10 h-10 flex items-center justify-center mb-2">
            <Image size={24} className="text-[#B31B1E]" />
          </div>
          <span className="text-sm font-medium text-black">Digi Album</span>
        </div>
      </div>



      {/* Active Tab Content */}
      <div className="mb-8 w-full">
        {activeTab === "checklist" && (
          <Checklist
            setError={setError}
            setSuccessMessage={setSuccessMessage}
            setLoading={setLoading}
            loading={loading}
            error={error}
            successMessage={successMessage}
          />
        )}
        {activeTab === "budget" && (
          <Budget
            setError={setError}
            setSuccessMessage={setSuccessMessage}
            setLoading={setLoading}
            loading={loading}
            error={error}
            successMessage={successMessage}
          />
        )}
        {activeTab === "guestlist" && (
          <GuestList
            setError={setError}
            setSuccessMessage={setSuccessMessage}
            setLoading={setLoading}
            loading={loading}
            error={error}
            successMessage={successMessage}
          />
        )}
        {activeTab === "vendors" && (
          <Vendors
            setError={setError}
            setSuccessMessage={setSuccessMessage}
            setLoading={setLoading}
            loading={loading}
            error={error}
            successMessage={successMessage}
          />
        )}
        {activeTab === "websites" && (
          <Websites
            setError={setError}
            setSuccessMessage={setSuccessMessage}
            setLoading={setLoading}
            loading={loading}
            error={error}
            successMessage={successMessage}
          />
        )}
        {activeTab === "digialbum" && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-bold mb-6 text-black">Digital Album</h2>
            <p className="text-gray-600">This feature is coming soon. Stay tuned!</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function WeddingToolsPage() {
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  useEffect(() => setIsClient(true), []);

  return (
    <div
      className={
        isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"
      }
    >
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
            width: "100%"
          }}
        >
          <div className="w-full mx-auto max-w-full">
            <Suspense fallback={<TabsLoading />}>
              <TabContent />
            </Suspense>
          </div>
        </main>
      </div>
    </div>
  );
}