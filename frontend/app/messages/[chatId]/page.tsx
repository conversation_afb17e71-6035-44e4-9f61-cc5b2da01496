"use client";

import { useState, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import Image from "next/image";
import EmojiPicker, { EmojiClickData } from "emoji-picker-react";

type Message = {
  message_id: string;
  sender_id: string;
  sender_name: string;
  sender_avatar: string | null;
  content: string;
  sent_at: string;
  is_read: boolean;
  attachment_url: string | null;
  is_mine: boolean;
};

export default function ChatPage() {
  const params = useParams();
  const chatId = params?.chatId as string;
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [conversationName, setConversationName] = useState("Chat");
  const [conversationAvatar, setConversationAvatar] = useState<string | null>(
    null
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch conversation details and messages
  useEffect(() => {
    async function fetchConversationAndMessages() {
      try {
        const token =
          localStorage.getItem("token") || sessionStorage.getItem("token");

        if (!token) {
          setError("Authentication token not found");
          setLoading(false);
          return;
        }

        // First, fetch conversation details to get the other user's info
        try {
          const conversationResponse = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/conversations/${chatId}`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (conversationResponse.ok) {
            const conversationData = await conversationResponse.json();
            console.log("Conversation API Response:", conversationData);

            // Extract conversation details
            let conversation;
            if (
              conversationData.body &&
              typeof conversationData.body === "string"
            ) {
              try {
                conversation = JSON.parse(conversationData.body);
              } catch (e) {
                console.error("Error parsing conversation from body:", e);
              }
            } else {
              conversation = conversationData;
            }

            if (conversation) {
              // Set conversation name and avatar
              if (!conversation.is_group && conversation.other_user) {
                setConversationName(conversation.other_user.name || "User");
                setConversationAvatar(conversation.other_user.profile_picture);
              } else {
                setConversationName(conversation.name || "Chat");
              }
            }
          }
        } catch (e) {
          console.error("Error fetching conversation details:", e);
          // Continue to fetch messages even if conversation details fail
        }

        // Then fetch messages
        const messagesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/messages?conversation_id=${chatId}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!messagesResponse.ok) {
          throw new Error(
            `Failed to fetch messages: ${messagesResponse.status}`
          );
        }

        const responseData = await messagesResponse.json();
        console.log("Messages API Response:", responseData);

        // Handle different response formats
        let messagesArray: Message[] = [];

        if (responseData.body && typeof responseData.body === "string") {
          // If the API returns data in a body property as a string
          try {
            messagesArray = JSON.parse(responseData.body);
          } catch (e) {
            console.error("Error parsing messages from body:", e);
          }
        } else if (Array.isArray(responseData)) {
          // If the API directly returns an array
          messagesArray = responseData;
        } else if (responseData.data && Array.isArray(responseData.data)) {
          // If the API returns data in a data property
          messagesArray = responseData.data;
        }

        // Ensure we always have an array and sort by time
        const validMessages = Array.isArray(messagesArray) ? messagesArray : [];

        // Sort messages by time (oldest first)
        const sortedMessages = [...validMessages].sort(
          (a, b) =>
            new Date(a.sent_at).getTime() - new Date(b.sent_at).getTime()
        );

        setMessages(sortedMessages);

        // If we couldn't get the conversation details, try to get them from messages
        if (conversationName === "Chat" && sortedMessages.length > 0) {
          const otherUserMessage = sortedMessages.find((msg) => !msg.is_mine);
          if (otherUserMessage) {
            setConversationName(otherUserMessage.sender_name);
            setConversationAvatar(otherUserMessage.sender_avatar);
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load messages");
      } finally {
        setLoading(false);
      }
    }

    if (chatId) {
      fetchConversationAndMessages();
    }

    // Optional: Set up a polling mechanism for real-time updates
    const interval = setInterval(() => {
      if (chatId) {
        fetchConversationAndMessages();
      }
    }, 10000); // Poll every 10 seconds

    return () => clearInterval(interval);
  }, [chatId, conversationName]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (!loading && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, loading]);

  async function handleSendMessage(e: React.FormEvent) {
    e.preventDefault();
    if (!newMessage.trim() || !chatId) return;

    try {
      const token =
        localStorage.getItem("token") || sessionStorage.getItem("token");

      if (!token) {
        setError("Authentication token not found");
        return;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/messages`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            conversation_id: chatId,
            content: newMessage,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.status}`);
      }

      const responseData = await response.json();
      console.log("Send Message Response:", responseData);

      // Handle different response formats
      let sentMessage: Message | null = null;

      if (responseData.body && typeof responseData.body === "string") {
        try {
          sentMessage = JSON.parse(responseData.body);
        } catch (e) {
          console.error("Error parsing sent message response:", e);
        }
      } else {
        sentMessage = responseData;
      }

      if (sentMessage) {
        setMessages((prev) => [...prev, sentMessage as Message]);
      }

      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
      alert("Failed to send message. Please try again.");
    }
  }

  if (loading) {
    return (
      <div className="flex flex-col h-full bg-white">
        <div
          className="p-4 border-b border-gray-200 flex items-center"
          style={{
            background:
              "linear-gradient(179.27deg, #FAE6C4 0.63%, #FFFFFF 149.12%)",
          }}
        >
          <h1 className="text-xl font-semibold text-black">Loading...</h1>
        </div>

        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-4"></div>
            <p>Loading messages...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-full bg-white">
        <div
          className="p-4 border-b border-gray-200 flex items-center"
          style={{
            background:
              "linear-gradient(179.27deg, #FAE6C4 0.63%, #FFFFFF 149.12%)",
          }}
        >
          <h1 className="text-xl font-semibold text-black">Error</h1>
        </div>

        <div className="flex-1 flex items-center justify-center p-4">
          <div className="bg-red-50 text-red-700 p-4 rounded-lg max-w-md w-full">
            <p className="font-medium">Error</p>
            <p>{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat header - fixed at the top */}
      <div
        className="p-4 border-b border-gray-200 flex items-center sticky top-0 z-10"
        style={{
          background:
            "linear-gradient(179.27deg, #FAE6C4 0.63%, #FFFFFF 149.12%)",
        }}
      >
        <div className="flex items-center">
          {conversationAvatar ? (
            <div className="relative w-10 h-10 rounded-full overflow-hidden mr-3">
              <Image
                src={conversationAvatar}
                alt={conversationName}
                fill
                className="object-cover"
                sizes="40px"
              />
            </div>
          ) : (
            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3">
              <span className="text-gray-500 font-medium">
                {conversationName.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          <h1 className="text-lg font-semibold truncate text-black">
            {conversationName}
          </h1>
        </div>
      </div>

      {/* Messages container - independently scrollable */}
      <div
        className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-100"
        style={{ maxHeight: "calc(100vh - 200px)" }}
      >
        {!Array.isArray(messages) ? (
          <div className="text-center text-red-500 my-8">
            Invalid message data format
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center text-gray-500 my-8">
            <div className="mb-4">
              <svg
                className="w-16 h-16 mx-auto text-gray-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
            </div>
            <p>No messages yet</p>
            <p className="mt-1 text-sm">
              Start the conversation by sending a message below
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.message_id}
              className={`flex ${
                message.is_mine ? "justify-end" : "justify-start"
              }`}
            >
              {!message.is_mine && (
                <div className="flex-shrink-0 mr-2">
                  {message.sender_avatar ? (
                    <div className="relative w-8 h-8 rounded-full overflow-hidden">
                      <Image
                        src={message.sender_avatar}
                        alt={message.sender_name}
                        fill
                        className="object-cover"
                        sizes="32px"
                      />
                    </div>
                  ) : (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-xs text-gray-500 font-medium">
                        {message.sender_name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>
              )}

              <div
                className={`text-black ${
                  message.is_mine
                    ? "rounded-tl-2xl rounded-tr-2xl rounded-bl-2xl"
                    : "rounded-tr-2xl rounded-br-2xl rounded-bl-2xl border border-[#D9D9D9]"
                }`}
                style={{
                  width: message.is_mine ? "230px" : "260px",
                  padding: "12px 18px 6px 18px",
                  gap: "4px",
                  background: message.is_mine ? "#FFC74766" : "#E3E3F7",
                }}
              >
                {message.content}
                <div
                  className={`text-xs mt-1 ${
                    message.is_mine ? "text-black" : "text-gray-500"
                  }`}
                >
                  {formatMessageTime(message.sent_at)}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message input - fixed at the bottom */}
      <form
        onSubmit={handleSendMessage}
        className="border-t border-gray-200 p-4 bg-gray-50 sticky bottom-0 z-10"
      >
        <div className="relative">
          {showEmojiPicker && (
            <div className="absolute bottom-14 left-0 z-10">
              <EmojiPicker
                onEmojiClick={(emojiData: EmojiClickData) => {
                  setNewMessage((prev) => prev + emojiData.emoji);
                  setShowEmojiPicker(false);
                }}
                width={300}
                height={400}
              />
            </div>
          )}
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              className="bg-gray-200 text-gray-600 rounded-full p-2 w-10 h-10 flex items-center justify-center hover:bg-gray-300"
            >
              <span className="text-xl">😊</span>
            </button>
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type a message..."
              className="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 text-black"
            />
            <button
              type="submit"
              disabled={!newMessage.trim()}
              className="bg-green-500 text-white rounded-full p-2 w-10 h-10 flex items-center justify-center disabled:opacity-50"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 12h14M12 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}

// Helper function to format message time
function formatMessageTime(timeString: string): string {
  try {
    const date = new Date(timeString);
    const now = new Date();

    // If message is from today, show time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    // If message is from this year, show month and day
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }

    // Otherwise show date with year
    return date.toLocaleDateString([], {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  } catch (e) {
    console.error("Error formatting date:", e);
    return timeString;
  }
}
