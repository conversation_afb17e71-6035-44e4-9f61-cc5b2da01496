"use client";
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import UserProfile from '../../components/userProfile/page';

export default function UserProfilePage() {
    const router = useRouter();
    const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

    useEffect(() => {
        // Check for authentication token
        const checkAuth = () => {
            const token = localStorage.getItem('token') ||
                localStorage.getItem('jwt_token') ||
                localStorage.getItem('wedzat_token');

            if (!token) {
                console.warn('No authentication token found, redirecting to login');
                router.push('/');
                setIsAuthenticated(false);
                return;
            }

            // Check if token is expired (if it's a JWT)
            try {
                const tokenParts = token.split('.');
                if (tokenParts.length === 3) {
                    // It's a JWT, decode the payload
                    const payload = JSON.parse(atob(tokenParts[1]));
                    const expTime = payload.exp * 1000; // Convert to milliseconds

                    if (expTime < Date.now()) {
                        console.warn('Token is expired, redirecting to login');
                        localStorage.removeItem('token');
                        localStorage.removeItem('jwt_token');
                        localStorage.removeItem('wedzat_token');
                        router.push('/');
                        setIsAuthenticated(false);
                        return;
                    }
                }
            } catch (err) {
                console.error('Error checking token expiration:', err);
                // Continue anyway, the API call will fail if the token is invalid
            }

            setIsAuthenticated(true);
        };

        checkAuth();

        // Add event listener for storage changes (e.g., token deletion)
        const handleStorageChange = (e: StorageEvent) => {
            if (e.key === 'token' || e.key === 'jwt_token' || e.key === 'wedzat_token') {
                if (!e.newValue) {
                    console.warn('Token removed, redirecting to login');
                    router.push('/');
                    setIsAuthenticated(false);
                }
            }
        };

        window.addEventListener('storage', handleStorageChange);

        return () => {
            window.removeEventListener('storage', handleStorageChange);
        };
    }, [router]);

    // Show loading state while checking authentication
    if (isAuthenticated === null) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
            </div>
        );
    }

    // Only render the profile if authenticated
    return isAuthenticated ? <UserProfile /> : null;
}