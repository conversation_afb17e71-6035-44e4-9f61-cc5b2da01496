// frontend/app/verify/page.tsx
'use client';

import { useState } from 'react';
import PhoneVerification from '../../components/PhoneVerification';

export default function VerificationPage() {
  const [verificationMethod, setVerificationMethod] = useState<'email' | 'phone'>('email');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState('');
  const [verified, setVerified] = useState(false);
  const [verifiedData, setVerifiedData] = useState<{email?: string, phone?: string}>({});

  // Handle sending OTP for email (server-side)
  const handleSendEmailOtp = async () => {
    try {
      setIsLoading(true);
      setMessage('');

      const response = await fetch('/api/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setOtpSent(true);
        setMessage('Verification code sent to your email');
        
        // For development, show OTP
        if (data.devOtp) {
          setMessage(`${data.message} (Dev OTP: ${data.devOtp})`);
        }
      } else {
        setMessage(data.message || 'Failed to send verification code');
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      setMessage('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle verifying OTP for email (server-side)
  const handleVerifyEmailOtp = async () => {
    try {
      setIsLoading(true);
      setMessage('');

      const response = await fetch('/api/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ otp }),
      });

      const data = await response.json();

      if (data.success) {
        setVerified(true);
        setVerifiedData({ email: data.email });
        setMessage('Email verification successful!');
      } else {
        setMessage(data.message || 'Invalid verification code');
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      setMessage('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resending OTP for email (server-side)
  const handleResendEmailOtp = async () => {
    try {
      setIsLoading(true);
      setMessage('');

      const response = await fetch('/api/resend-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setMessage('Verification code resent to your email');
        
        // For development, show OTP
        if (data.devOtp) {
          setMessage(`${data.message} (Dev OTP: ${data.devOtp})`);
        }
      } else {
        setMessage(data.message || 'Failed to resend verification code');
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      setMessage('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Firebase phone verification success
  const handlePhoneVerificationSuccess = async (verifiedPhone: string) => {
    try {
      setIsLoading(true);
      setMessage('');

      // Notify the server about successful Firebase verification
      const response = await fetch('/api/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          firebaseVerified: true,
          verifiedPhone: verifiedPhone,
          otp: 'firebase-verified' // Placeholder, not used for Firebase verification
        }),
      });

      const data = await response.json();

      if (data.success) {
        setVerified(true);
        setVerifiedData({ phone: data.phone });
        setMessage('Phone verification successful!');
      } else {
        setMessage(data.message || 'Verification failed on server');
      }
    } catch (error) {
      console.error('Error with server verification:', error);
      setMessage('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle Firebase verification error
  const handleVerificationError = (errorMessage: string) => {
    setMessage(errorMessage);
  };

  // Reset verification state
  const handleReset = () => {
    setVerified(false);
    setOtpSent(false);
    setMessage('');
    setOtp('');
    setVerifiedData({});
  };

  return (
    <div className="verification-container">
      <h1>Account Verification</h1>
      
      {!verified ? (
        <>
          {/* Method selection */}
          {!otpSent && (
            <div className="method-selection">
              <div className="tabs">
                <button 
                  className={verificationMethod === 'email' ? 'active' : ''}
                  onClick={() => setVerificationMethod('email')}
                  disabled={isLoading}
                >
                  Verify with Email
                </button>
                <button 
                  className={verificationMethod === 'phone' ? 'active' : ''}
                  onClick={() => setVerificationMethod('phone')}
                  disabled={isLoading}
                >
                  Verify with Phone
                </button>
              </div>
              
              {/* Email verification */}
              {verificationMethod === 'email' && (
                <div className="email-container">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                  />
                  <button 
                    onClick={handleSendEmailOtp}
                    disabled={!email || isLoading}
                  >
                    {isLoading ? 'Sending...' : 'Send Verification Code'}
                  </button>
                </div>
              )}
              
              {/* Phone verification */}
              {verificationMethod === 'phone' && (
                <div className="phone-container">
                  <PhoneVerification
                    onVerificationSuccess={handlePhoneVerificationSuccess}
                    onError={handleVerificationError}
                  />
                </div>
              )}
            </div>
          )}
          
          {/* OTP verification for email */}
          {otpSent && verificationMethod === 'email' && (
            <div className="otp-container">
              <input
                type="text"
                placeholder="Enter verification code"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                disabled={isLoading}
              />
              <button 
                onClick={handleVerifyEmailOtp}
                disabled={!otp || isLoading}
              >
                {isLoading ? 'Verifying...' : 'Verify Code'}
              </button>
              <button 
                onClick={handleResendEmailOtp}
                disabled={isLoading}
                className="resend-button"
              >
                Resend Code
              </button>
              <button 
                onClick={() => setOtpSent(false)}
                disabled={isLoading}
                className="back-button"
              >
                Change Email
              </button>
            </div>
          )}
        </>
      ) : (
        <div className="success-container">
          <h2>Verification Successful!</h2>
          {verifiedData.email && <p>Email: {verifiedData.email}</p>}
          {verifiedData.phone && <p>Phone: {verifiedData.phone}</p>}
          <button onClick={handleReset}>Verify Another Method</button>
        </div>
      )}
      
      {message && <div className="message">{message}</div>}
    </div>
  );
}