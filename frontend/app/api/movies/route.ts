import { NextResponse } from 'next/server';

// Mock data for movies
const allMovies = Array.from({ length: 40 }, (_, index) => ({
  video_id: `movie-${index + 1}`,
  video_name: `Wedding Movie ${index + 1}`,
  video_url: `https://www.youtube.com/watch?v=dQw4w9WgXcQ`,
  video_description: `This is a description for wedding movie ${index + 1}`,
  video_thumbnail: `/pics/placeholder.svg`,
  video_duration: 600 + index * 60, // Longer videos
  video_category: "Wedding",
  created_at: new Date(Date.now() - index * 86400000).toISOString(), // Each video is one day older
  user_name: `User ${Math.floor(index / 5) + 1}`, // Group videos by user
  user_id: `user-${Math.floor(index / 5) + 1}`,
  is_own_content: false,
  video_views: 5000 * (index + 1),
  video_likes: 500 * (index + 1),
  video_comments: 50 * (index + 1)
}));

export async function GET(request: Request) {
  // Get URL and search params
  const { searchParams } = new URL(request.url);
  
  // Parse pagination parameters
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const excludeId = searchParams.get('exclude');
  
  // Calculate pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  // Filter out excluded ID if provided
  const filteredMovies = excludeId 
    ? allMovies.filter(movie => movie.video_id !== excludeId)
    : allMovies;
  
  // Get the current page of movies
  const paginatedMovies = filteredMovies.slice(startIndex, endIndex);
  
  // Check if there are more pages
  const hasNextPage = endIndex < filteredMovies.length;
  
  // Log for debugging
  console.log(`[Mock API] Returning page ${page} with ${paginatedMovies.length} movies. Has next page: ${hasNextPage}`);
  
  // Add a delay to simulate network latency (200-800ms)
  await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 600));
  
  return NextResponse.json({
    movies: paginatedMovies,
    next_page: hasNextPage,
    total_count: filteredMovies.length,
    current_page: page
  });
}
