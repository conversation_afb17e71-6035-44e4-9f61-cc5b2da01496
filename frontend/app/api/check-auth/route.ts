// API route to check if the user is authenticated
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

interface AuthCheckResponse {
  isAuthenticated: boolean;
  method: string | null;
  email: string | null;
  phone: string | null;
}

export async function GET(): Promise<NextResponse<AuthCheckResponse>> {
  try {
    const verified = (await cookies()).get('verified')?.value;
    const verifyMethod = (await cookies()).get('verifyMethod')?.value;
    
    let email: string | null = null;
    let phone: string | null = null;
    
    if (verifyMethod === 'email') {
      email = (await cookies()).get('verifyEmail')?.value || null;
    } else if (verifyMethod === 'phone') {
      phone = (await cookies()).get('verifyPhone')?.value || null;
    }
    
    return NextResponse.json({ 
      isAuthenticated: verified === 'true',
      method: verifyMethod || null,
      email,
      phone
    });
  } catch (error) {
    console.error('Error checking auth status:', error);
    return NextResponse.json({ 
      isAuthenticated: false,
      method: null,
      email: null,
      phone: null
    });
  }
}