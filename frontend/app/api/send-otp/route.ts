// frontend/app/api/send-otp/route.ts
import { cookies } from 'next/headers';
import { generateOTP } from '../../../lib/otp';
import { sendOtpEmail } from '../../../lib/nodemailer';
import { NextResponse } from 'next/server';

interface RequestBody {
  email?: string;
  phone?: string;
  useFirebase?: boolean; // New flag to indicate Firebase usage for phone
}

interface ResponseData {
  success: boolean;
  message: string;
  emailSent?: boolean;
  useFirebase?: boolean; // Indicates client should use Firebase for phone verification
  devOtp?: string; // Only for development
}

export async function POST(request: Request): Promise<NextResponse<ResponseData>> {
  try {
    const body: RequestBody = await request.json();
    const { email, phone, useFirebase = true } = body; // Default to using Firebase for phone
    
    if (!email && !phone) {
      return NextResponse.json({ 
        success: false, 
        message: "Either email or phone is required" 
      }, { status: 400 });
    }
    
    // Track delivery status
    let emailSent: boolean = false;
    let otp: string = '';
    
    // For phone verification with Firebase
    if (phone && useFirebase) {
      // With Firebase, we don't send O<PERSON> from server for phone numbers
      // Just set a flag indicating client should use Firebase
      
      // Store verification method
      (await cookies()).set({
        name: 'verifyMethod',
        value: 'phone',
        httpOnly: true,
        maxAge: 600, // 10 minutes
        path: '/',
        sameSite: 'strict',
      });
      
      (await cookies()).set({
        name: 'verifyPhone',
        value: phone,
        httpOnly: true,
        maxAge: 600,
        path: '/',
        sameSite: 'strict',
      });
      
      (await cookies()).set({
        name: 'useFirebase',
        value: 'true',
        httpOnly: true,
        maxAge: 600,
        path: '/',
        sameSite: 'strict',
      });
      
      console.log(`Phone verification for ${phone} will use Firebase`);
    }
    
    // For email verification - keep existing Nodemailer method
    if (email) {
      try {
        // Generate OTP for email
        otp = generateOTP(6);
        
        // Always log OTP in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log(`[DEV] Generated OTP for email ${email}: ${otp}`);
        }
        
        // Store OTP and verification method
        (await cookies()).set({
          name: 'pendingOtp',
          value: otp,
          httpOnly: true,
          maxAge: 600, // 10 minutes
          path: '/',
          sameSite: 'strict',
        });
        
        (await cookies()).set({
          name: 'verifyMethod',
          value: 'email',
          httpOnly: true,
          maxAge: 600,
          path: '/',
          sameSite: 'strict',
        });
        
        (await cookies()).set({
          name: 'verifyEmail',
          value: email,
          httpOnly: true,
          maxAge: 600,
          path: '/',
          sameSite: 'strict',
        });
        
        // Send OTP via email
        await sendOtpEmail(email, otp);
        emailSent = true;
        
        console.log(`Email verification sent to ${email}`);
      } catch (emailError) {
        console.error('Failed to send email:', emailError);
        
        // For development - fallback to console logging
        if (process.env.NODE_ENV === 'development') {
          console.log(`DEV MODE - Email OTP for ${email}: ${otp}`);
        }
      }
    }
    
    // Check if at least one delivery method succeeded or we're in development mode
    // For Firebase phone auth, we're just indicating client should handle it
    if (!emailSent && !phone && process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ 
        success: false, 
        message: "Failed to send verification through any channel" 
      }, { status: 500 });
    }

    if (process.env.NODE_ENV === 'development' && otp) {
      console.log('\n-----------------------------------------');
      console.log(`🔑 DEVELOPMENT OTP: ${otp}`);
      console.log('-----------------------------------------\n');
    }
    
    // For development mode, always return success and include OTP for testing
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ 
        success: true, 
        message: "Verification initiated", 
        emailSent,
        useFirebase: phone ? useFirebase : undefined,
        devOtp: otp || undefined
      });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: "Verification initiated", 
      emailSent,
      useFirebase: phone ? useFirebase : undefined
    });
  } catch (error) {
    console.error('Error in send-otp:', error);
    return NextResponse.json({ 
      success: false, 
      message: "Internal server error" 
    }, { status: 500 });
  }
}