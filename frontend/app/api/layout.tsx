import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { <PERSON><PERSON>rov<PERSON> } from '@clerk/nextjs';
import { AuthProvider } from "../../contexts/AuthContext";

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Wedzat - Your Wedding Platform',
  description: 'Your all-in-one wedding planning platform',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ClerkProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}