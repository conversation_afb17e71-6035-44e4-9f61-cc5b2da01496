'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import WedzatLoginPage from '../components/WedzatLoginpage';
import { useAuth } from '../contexts/AuthContext';
import UserProfile from '../components/userProfile/page';

export default function Page() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // If authenticated, redirect to home
    if (isAuthenticated) {
      console.log('Already authenticated, redirecting to home');
      router.push('/home');
    }
  }, [isAuthenticated, router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700"></div>
      </div>
    );
  }

  // If not authenticated, show login page
  return <WedzatLoginPage />;
}