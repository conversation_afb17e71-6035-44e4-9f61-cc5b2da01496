"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import {
  TopNavigation,
  SideNavigation,
  MobileNavigation,
  RightSidebar,
} from "./Navigation";
import WeddingVideosSection from "./WeddingVideos";
import Glimpses from "./Glimpses";
import FlashesSection from "./Flashes";
import Photos from "./Photos";
import Stories from "./Stories";
import LazyLoadComponent from "./LazyLoadComponent";

// Main HomeDashboard component
function HomeDashboard() {
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [rightSidebarExpanded, setRightSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  useEffect(() => setIsClient(true), []);
  // Set a timestamp for cache busting if needed
  useEffect(() => {
    console.log('HomeDashboard mounted at:', Date.now());
  }, []);

  return (
    <div
      className={
        isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"
      }
    >
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 py-4 pr-4 pl-0 bg-white ${sidebarExpanded ? "md:ml-48" : "md:ml-20"
            }`}
          style={{
            marginTop: "80px", // Height of TopNavigation
            transition: "all 300ms ease-in-out",
            minHeight: "calc(100vh - 80px)",
            paddingBottom: "40px", // Add padding at the bottom for better spacing
            overflowY: "auto",
            overflowX: "hidden",
            marginRight: rightSidebarExpanded ? "320px" : "0",
            paddingRight: "20px",
            paddingLeft: "0", // Remove left padding to reduce the gap
            display: "flex",
            justifyContent: "flex-start", // Align content to the left instead of center
          }}
        >
          {/* Content Container */}
          <div className="flex flex-col gap-8 max-w-[1100px] w-full pl-2">
            {/* Stories are loaded immediately */}
            <div className="overflow-x-auto">
              <Stories />
            </div>

            {/* Categories are loaded immediately */}
            <div className="overflow-x-auto">
              <CategoriesSection />
            </div>

            {/* Lazy load sections with larger rootMargin values to stagger loading */}
            <LazyLoadComponent
              id="glimpses-section"
              rootMargin="0px 0px 500px 0px" // Load when 500px from viewport
              placeholder={
                <div className="py-6 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading glimpses...</span>
                </div>
              }
            >
              {({ isVisible, shouldLoad }) => (
                <div className="overflow-x-auto">
                  {isVisible ? <Glimpses shouldLoad={shouldLoad} /> : null}
                </div>
              )}
            </LazyLoadComponent>

            {/* Lazy load Special Offers when they come into view */}
            <LazyLoadComponent
              id="special-offers-section"
              rootMargin="0px 0px 400px 0px" // Load when 400px from viewport
              placeholder={
                <div className="py-6 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading special offers...</span>
                </div>
              }
            >
              {({ isVisible, shouldLoad }) => (
                <div className="overflow-x-auto">
                  {isVisible ? <SpecialOffersSection shouldLoad={shouldLoad} /> : null}
                </div>
              )}
            </LazyLoadComponent>

            {/* Lazy load Flashes when they come into view */}
            <LazyLoadComponent
              id="flashes-section"
              rootMargin="0px 0px 300px 0px" // Load when 300px from viewport
              placeholder={
                <div className="py-6 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading flashes...</span>
                </div>
              }
            >
              {({ isVisible, shouldLoad }) => (
                <div className="overflow-x-auto">
                  {isVisible ? <FlashesSection shouldLoad={shouldLoad} /> : null}
                </div>
              )}
            </LazyLoadComponent>

            {/* Lazy load Wedding Videos when they come into view */}
            <LazyLoadComponent
              id="wedding-videos-section"
              rootMargin="0px 0px 200px 0px" // Load when 200px from viewport
              placeholder={
                <div className="py-6 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading wedding videos...</span>
                </div>
              }
            >
              {({ isVisible, shouldLoad }) => (
                <div className="overflow-x-auto">
                  {isVisible ? <WeddingVideosSection shouldLoad={shouldLoad} /> : null}
                </div>
              )}
            </LazyLoadComponent>

            {/* Lazy load Photos when they come into view */}
            <LazyLoadComponent
              id="photos-section"
              rootMargin="0px 0px 100px 0px" // Load when 100px from viewport
              placeholder={
                <div className="py-6 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading photos...</span>
                </div>
              }
            >
              {({ isVisible, shouldLoad }) => (
                <div className="overflow-x-auto">
                  {isVisible ? <Photos shouldLoad={shouldLoad} /> : null}
                </div>
              )}
            </LazyLoadComponent>
          </div>
        </main>

        <RightSidebar
          expanded={rightSidebarExpanded}
          onExpand={() => setRightSidebarExpanded(true)}
          onCollapse={() => setRightSidebarExpanded(false)}
        />
      </div>

      <MobileNavigation />
    </div>
  );
}

export default HomeDashboard;

// UserProfiles component has been replaced with Stories component

import { useRouter } from "next/navigation";

const CategoriesSection: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Function to handle horizontal scrolling
  const scrollHorizontally = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = 300; // Adjust scroll amount as needed

      if (direction === 'left') {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
    }
  };

  const categories = [
    {
      name: "Venues",
      id: 1,
      image:
        "https://images.unsplash.com/photo-1519167758481-83f550bb49b3?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Photography",
      id: 2,
      image:
        "https://images.unsplash.com/photo-1520854221256-17451cc331bf?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Videographer",
      id: 3,
      image:
        "https://images.unsplash.com/photo-1604152135912-04a022e23696?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Makeup Artist",
      id: 4,
      image:
        "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Decoration",
      id: 5,
      image:
        "https://images.unsplash.com/photo-1519741497674-611481863552?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Caterers",
      id: 6,
      image:
        "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Mehendi",
      id: 7,
      image:
        "https://images.unsplash.com/photo-1525135850648-b42365991054?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Bridal Outfit",
      id: 8,
      image:
        "https://images.unsplash.com/photo-1585241920473-b472eb9ffbae?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Groom Outfit",
      id: 9,
      image:
        "https://images.unsplash.com/photo-1596436889106-be35e843f974?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Invitations",
      id: 10,
      image:
        "https://images.unsplash.com/photo-1607190074257-dd4b7af0309f?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Music & Dance",
      id: 11,
      image:
        "https://images.unsplash.com/photo-1429962714451-bb934ecdc4ec?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Pandits",
      id: 12,
      image:
        "https://images.unsplash.com/photo-1639575668910-9a14063add58?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Jewelry",
      id: 13,
      image:
        "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Dermatologist",
      id: 14,
      image:
        "https://images.unsplash.com/photo-1612776572997-76cc42e058c3?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Esthetic Dentist",
      id: 15,
      image:
        "https://images.unsplash.com/photo-1606811971618-4486d14f3f99?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Beauty Grooming",
      id: 16,
      image:
        "https://images.unsplash.com/photo-1560750588-73207b1ef5b8?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Event Planners",
      id: 17,
      image:
        "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Transportation",
      id: 18,
      image:
        "https://images.unsplash.com/photo-1515876305430-f06edab8282a?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Hospitality",
      id: 19,
      image:
        "https://images.unsplash.com/photo-1566073771259-6a8506099945?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Gifts",
      id: 20,
      image:
        "https://images.unsplash.com/photo-1549465220-1a8b9238cd48?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Astrologers",
      id: 21,
      image:
        "https://images.unsplash.com/photo-1515942661900-94b3d1972591?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Honeymoon Planners",
      id: 22,
      image:
        "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Bridal Shower",
      id: 23,
      image:
        "https://images.unsplash.com/photo-1556035511-3168381ea4d4?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Baby Shower",
      id: 24,
      image:
        "https://images.unsplash.com/photo-1544006659-f0b21884ce1d?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Bachelor Party",
      id: 25,
      image:
        "https://images.unsplash.com/photo-1575444758702-4a6b9222336e?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Wedding Planner",
      id: 26,
      image:
        "https://images.unsplash.com/photo-1469371670807-013ccf25f16a?q=80&w=200&h=120&fit=crop",
    },
    {
      name: "Others",
      id: 27,
      image:
        "https://images.unsplash.com/photo-1523438885200-e635ba2c371e?q=80&w=200&h=120&fit=crop",
    },
  ];

  // Calculate the actual total number of categories
  const totalCategories = categories.length;

  // Handle navigation - these now control both pagination display and can be used for scrolling
  const goToNextPage = () => {
    setCurrentPage((prev) => {
      // If we're at the last page, go back to the first page
      if (prev >= totalCategories - 1) {
        return 0;
      }
      // Otherwise, go to the next page
      return prev + 1;
    });
    scrollHorizontally('right');
  };

  const goToPrevPage = () => {
    setCurrentPage((prev) => {
      // If we're at the first page, go to the last page
      if (prev <= 0) {
        return totalCategories - 1;
      }
      // Otherwise, go to the previous page
      return prev - 1;
    });
    scrollHorizontally('left');
  };

  return (
    <section
      className="mb-8 relative bg-white pb-4"
      style={{
        borderBottom: "2px dashed #9CA3AF",
        minHeight: "150px", // Increased height to accommodate navigation
      }}
    >
      {/* Horizontal scroll container with navigation buttons */}
      {/* Fixed "See All" button that doesn't move with scrolling */}
      <div className="flex justify-between items-center mb-2 px-6">
        <h2 className="font-inter text-[16px] leading-[18px] tracking-[0%] align-middle font-semibold text-black">
          Vendor Category
        </h2>
        <div className="flex items-center gap-2">
          {/* Pagination */}
          <button
            onClick={goToPrevPage}
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200"
            aria-label="Previous page"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <span className="text-sm text-gray-500">
            {currentPage + 1} / {totalCategories}
          </span>
          <button
            onClick={goToNextPage}
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200"
            aria-label="Next page"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
          <a
            href="/home/<USER>"
            className="ml-4 font-inter text-[13px] leading-[18px] font-semibold text-[#B31B1E] hover:underline align-middle tracking-[0%] px-3 py-1.5 rounded-md hover:bg-red-50 see-all-link"
            style={{
              padding: "6px 1px",     // Inner spacing
              //border: "1px solid #f8d7d7", // Light border
              borderRadius: "0px",     // Rounded corners
              transition: "all 0.2s ease", // Smooth transition for hover effects
            }}
          >
            See All
          </a>
        </div>
      </div>

      <div className="relative">
        {/* Left scroll button */}
        <button
          onClick={() => scrollHorizontally('left')}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-white shadow-md hover:bg-gray-100"
          aria-label="Scroll left"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>

        {/* Right scroll button */}
        <button
          onClick={() => scrollHorizontally('right')}
          className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-white shadow-md hover:bg-gray-100"
          aria-label="Scroll right"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </button>

        <div 
          ref={scrollContainerRef}
          className="flex overflow-x-auto scrollbar-hide gap-5 mt-2 pb-4 px-2"
          style={{
            scrollSnapType: "x mandatory", // Improve scroll behavior
            WebkitOverflowScrolling: "touch", // Better scrolling on iOS
          }}
        >
          {categories.map((category) => (
            <div
              key={category.id}
              style={{
                width: "140px", // Fixed width on all devices
                minWidth: "140px", // Ensure minimum width
                height: "82px",
                borderRadius: "10px",
                padding: "12px",
                border: "1px solid #D9D9D9",
                boxShadow:
                  "0px 4px 14px 0px #00000026, inset 4px 4px 14px 0px #9747FF1A",
                flex: "none",
                backgroundImage: `url(${category.image})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                scrollSnapAlign: "start", // Improve scroll behavior
              }}
              className="relative overflow-hidden hover:scale-105 transition-transform cursor-pointer"
              onClick={() => {
                if (typeof window !== 'undefined') {
                  // Use window.location as a fallback if router isn't available
                  window.location.href = `/home/<USER>
                }
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div className="absolute bottom-2 left-2 text-white font-medium text-sm">
                {category.name}
              </div>
            </div>
          ))}
        </div>
        
        {/* Add "See All" link at the bottom - hidden on mobile */}
        <div className="hidden md:flex justify-center mt-4">
          <a
            href="/home/<USER>"
            className="font-inter text-[13px] leading-[18px] font-semibold text-[#B31B1E] hover:underline align-middle tracking-[0%] px-3 py-1.5 rounded-md hover:bg-red-50"
            style={{
              padding: "6px 12px",
              border: "1px solid #f8d7d7",
              borderRadius: "6px",
              transition: "all 0.2s ease",
            }}
          >
            See All Categories
          </a>
        </div>
      </div>

      
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        
        .see-all-link {
          margin-left: 12px;
          margin-right: 8px;
        }
        
        /* Mobile styles */
        @media (max-width: 768px) {
          .see-all-link {
            margin-right: 8px;
          }
        }
        
        /* Desktop styles */
        @media (min-width: 769px) {
          .see-all-link {
            margin-right: 8px;
          }
        }
      `}</style>
    </section>
  );
};

// Removed unused GlimpsesSection component

interface SpecialOffersProps {
  shouldLoad?: boolean;
}

const SpecialOffersSection: React.FC<SpecialOffersProps> = ({ shouldLoad = false }) => {
  // Import UserAvatar at the component level if needed
  // const UserAvatar = dynamic(() => import('./UserAvatar'), { ssr: false });

  // Use shouldLoad prop to conditionally load data if needed
  useEffect(() => {
    if (shouldLoad) {
      console.log('Special Offers component is now visible and ready to load data');
      // Add any data loading logic here if needed
    }
  }, [shouldLoad]);
  return (
    <section className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="font-inter text-[16px] leading-[18px] tracking-[0%] align-middle font-semibold text-black">
          Special Offers
        </h2>
      </div>
      <div
        className="rounded-[10px] overflow-hidden shadow-md"
        style={{
          width: "772px",
          height: "160px",
          padding: "17px 23px",
          gap: "10px",
          backgroundColor: "#00376B",
        }}
      >
        {/* Inner container */}
        <div
          style={{
            width: "726px",
            height: "126px",
            gap: "38px",
            display: "flex",
            alignItems: "center",
          }}
        >
          {/* Logo container */}
          <div
            style={{
              width: "130.39535522460938px",
              height: "126px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Image
              src="/pics/image_13.png" // or image_13.jpg depending on the actual file extension
              alt="Special Offer"
              width={89}
              height={86}
              style={{
                objectFit: "contain",
              }}
              priority
            />
          </div>

          {/* Text content container */}
          <div
            style={{
              width: "557.6046142578125px",
              height: "100px",
              gap: "20px",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {/* Title and description container */}
            <div
              style={{
                width: "557.6046142578125px",
                height: "42px",
                gap: "6px",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <h3
                style={{
                  fontFamily: "Inter",
                  fontWeight: 400,
                  fontSize: "13px",
                  lineHeight: "18px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#FFFFFF",
                }}
              >
                Marriage Coupons
              </h3>
              <p
                style={{
                  fontFamily: "Roboto",
                  fontWeight: 400,
                  fontSize: "13px",
                  lineHeight: "18px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#CACACA",
                }}
              >
                Top to Bottom All Works Available for 5 Lakhs for 1300 peoples
                Food Day and night!
              </p>
            </div>

            <button
              style={{
                width: "116px",
                height: "38px",
                gap: "10px",
                padding: "10px 20px",
                borderRadius: "10px",
                backgroundColor: "#9100CA",
                border: "1px solid #28163E",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontFamily: "Inter",
                fontWeight: 500,
                fontSize: "13px",
                lineHeight: "18px",
                letterSpacing: "0%",
                verticalAlign: "middle",
                color: "#FFFFFF",
              }}
            >
              Buy Coupon
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

// const FlashesSection: React.FC = () => {
//   const flashes = [
//     {
//       id: 1,
//       username: "levrishamilton",
//       name: "Kirti Chadha",
//       follower: "chirag_singla17",
//     },
//     {
//       id: 2,
//       username: "levrishamilton",
//       name: "Kirti Chadha",
//       follower: "chirag_singla17",
//     },
//     {
//       id: 3,
//       username: "levrishamilton",
//       name: "Kirti Chadha",
//       follower: "chirag_singla17",
//     },
//     {
//       id: 4,
//       username: "levrishamilton",
//       name: "Kirti Chadha",
//       follower: "chirag_singla17",
//     },
//   ];

//   return (
//     <section className="mb-8">
//       <div className="flex items-center justify-between mb-4">
//         <h2 className="text-xl font-semibold">Flashes</h2>
//         <a
//           href="#"
//           className="text-red-500 text-sm font-medium hover:underline"
//         >
//           See all
//         </a>
//       </div>
//       <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
//         {flashes.map((flash) => (
//           <div
//             key={flash.id}
//             className="rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform"
//           >
//             <div className="h-40 bg-gray-200"></div>
//             <div className="absolute top-2 left-2">
//               <UserAvatar
//                 username={flash.username}
//                 size="sm"
//                 isGradientBorder={true}
//               />
//             </div>
//             <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white">
//               <div className="text-sm font-medium">{flash.name}</div>
//               <div className="text-xs">Followed by {flash.follower}</div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </section>
//   );
// };
