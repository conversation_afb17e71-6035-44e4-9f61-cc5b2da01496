import React from 'react';
import Image from 'next/image';
import { User } from 'lucide-react';

interface UserAvatarProps {
    username?: string;
    size?: 'xs' | 'sm' | 'md' | 'lg';
    showUsername?: boolean;
    isGradientBorder?: boolean;
    imageUrl?: string;
    onClick?: () => void;
}

const UserAvatar: React.FC<UserAvatarProps> = ({
    username,
    size = 'md',
    showUsername = false,
    isGradientBorder = false,
    imageUrl,
    onClick
}) => {
    // Size mappings
    const sizeClasses = {
        xs: 'w-4 h-4',
        sm: 'w-8 h-8',
        md: 'w-12 h-12',
        lg: 'w-16 h-16'
    };

    // Text size based on avatar size
    const textSizes = {
        xs: 'text-[8px]',
        sm: 'text-xs',
        md: 'text-sm',
        lg: 'text-base'
    };
    console.log("img url", imageUrl);
    return (
        <div className="flex flex-col items-center">
            <div
                className={`${sizeClasses[size]} rounded-full overflow-hidden ${isGradientBorder ? 'p-0.5 bg-gradient-to-br from-purple-400 to-pink-500' : ''
                    } ${onClick ? 'cursor-pointer hover:opacity-90' : ''}`}
                onClick={onClick}
            >
                <div className={`${isGradientBorder ? 'bg-white rounded-full w-full h-full' : ''} flex items-center justify-center`}>
                    {imageUrl ? (
                        <div className="relative w-full h-full">
                            {imageUrl.startsWith('data:') || !imageUrl.startsWith('/') ? (
                                // For base64 images or absolute URLs
                                <img
                                    src={imageUrl}
                                    alt={username || 'User'}
                                    className="w-full h-full object-cover rounded-full"
                                    onError={(e) => {
                                        console.error(`Failed to load avatar image: ${imageUrl}`);
                                        const imgElement = e.target as HTMLImageElement;
                                        if (imgElement) {
                                            imgElement.src = '/pics/placeholder.svg';
                                        }
                                    }}
                                />
                            ) : (
                                // For Next.js Image component (only for relative URLs)
                                <Image
                                    src={imageUrl}
                                    alt={username || 'User'}
                                    fill
                                    sizes={size === 'xs' ? '16px' : size === 'sm' ? '32px' : size === 'md' ? '48px' : '64px'}
                                    className="object-cover rounded-full"
                                    unoptimized={imageUrl.startsWith('/pics/') || imageUrl.includes('cloudfront.net')} // Skip optimization for local images and cloudfront URLs
                                    onError={() => {
                                        console.error(`Failed to load avatar image: ${imageUrl}`);
                                        // Error handling is managed by the fallback UI
                                    }}
                                />
                            )}
                        </div>
                    ) : (
                        <div className={`${sizeClasses[size]} bg-gray-200 rounded-full flex items-center justify-center`}>
                            <User className="text-gray-500" size={size === 'sm' ? 16 : size === 'md' ? 24 : 32} />
                        </div>
                    )}
                </div>
            </div>

            {showUsername && username && (
                <span className={`${textSizes[size]} mt-1 text-center truncate max-w-full`}>
                    {username}
                </span>
            )}
        </div>
    );
};

export default UserAvatar;
