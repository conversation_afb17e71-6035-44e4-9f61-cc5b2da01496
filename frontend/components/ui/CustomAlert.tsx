'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { X, AlertTriangle, CheckCircle, Info } from 'lucide-react';

interface CustomAlertProps {
  title: string;
  message: string;
  type?: 'error' | 'warning' | 'success' | 'info';
  onClose: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  title,
  message,
  type = 'info',
  onClose,
  onConfirm,
  confirmText = 'OK',
  cancelText = 'Cancel'
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Prevent scrolling of the background when alert is open
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300); // Match transition duration
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    handleClose();
  };

  // Determine icon and colors based on type
  const getTypeStyles = () => {
    switch (type) {
      case 'error':
        return {
          icon: <AlertTriangle size={20} className="text-red-600" />,
          confirmBtnColor: 'bg-red-600 hover:bg-red-700',
        };
      case 'warning':
        return {
          icon: <AlertTriangle size={20} className="text-red-600" />,
          confirmBtnColor: 'bg-red-600 hover:bg-red-700',
        };
      case 'success':
        return {
          icon: <CheckCircle size={20} className="text-red-600" />,
          confirmBtnColor: 'bg-red-600 hover:bg-red-700',
        };
      case 'info':
      default:
        return {
          icon: <Info size={20} className="text-red-600" />,
          confirmBtnColor: 'bg-red-600 hover:bg-red-700',
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div
      className={`fixed inset-x-0 top-0 flex justify-center z-[9999] transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div
        className={`bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-3 sm:p-4 relative overflow-y-auto max-h-[50vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%] transition-transform duration-300 shadow-lg mt-4 ${
          isVisible ? 'translate-y-0' : '-translate-y-full'
        }`}
      >
        {/* Close button */}
        <button
          onClick={handleClose}
          className="absolute top-2 right-2 text-gray-800 hover:text-red-600"
        >
          <X size={18} />
        </button>

        {/* Header */}
        <div className="flex items-center mb-3">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={24}
              height={24}
              className="object-cover"
            />
          </div>
          <h2 className="text-lg font-bold">{title}</h2>
        </div>

        {/* Alert content */}
        <div className="mb-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-2">
              {styles.icon}
            </div>
            <div className="flex-1">
              <p className="whitespace-pre-line text-gray-700 text-sm">{message}</p>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex justify-end space-x-3">
          {onConfirm && (
            <button
              onClick={handleClose}
              className="flex items-center justify-center px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm"
            >
              {cancelText}
            </button>
          )}
          <button
            onClick={handleConfirm}
            className={`flex items-center justify-center px-4 py-1.5 text-white rounded-md text-sm ${styles.confirmBtnColor}`}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomAlert;
