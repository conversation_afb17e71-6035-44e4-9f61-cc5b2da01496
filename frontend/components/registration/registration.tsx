// components/registration/registration.tsx
import React, { useState, useEffect, ChangeEvent, FormEvent } from "react";
import Image from "next/image";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { authService } from "../../services/api"; // Import the API service

interface RegistrationProps {
  onComplete?: (userData: {
    name: string;
    dateOfBirth: string;
    maritalStatus: string;
    place: string;
  }) => void;
  onBack?: () => void;
  // Additional props to handle authentication
  email?: string;
  mobileNumber?: string;
  password?: string;
}

const Registration: React.FC<RegistrationProps> = ({
  onComplete,
  onBack,
  email: propEmail,
  mobileNumber: propMobileNumber,
  password: propPassword
}) => {
  const router = useRouter();
  const [userData, setUserData] = useState({
    name: "",
    dateOfBirth: "",
    maritalStatus: "",
    place: "",
    mobileNumber: propMobileNumber || "",
    // Email and password will be set from sessionStorage or props
    email: propEmail || "",
    password: propPassword || ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [registrationType, setRegistrationType] = useState<'email' | 'mobile'>('email');
  const [debugInfo, setDebugInfo] = useState("");

  // Load verified email/mobile and password from sessionStorage on component mount
  useEffect(() => {
    const verifiedEmail = sessionStorage.getItem('verifiedEmail');
    const verifiedMobile = sessionStorage.getItem('verifiedMobile');
    const userPassword = sessionStorage.getItem('userPassword');
    
    // Determine registration type based on what was verified

  // Determine registration type based on what was verified
  if (verifiedEmail || propEmail) {
    setRegistrationType('email');
  } else if (verifiedMobile || propMobileNumber) {
    setRegistrationType('mobile');
  }
    
    // Update user data
    setUserData(prev => ({
      ...prev,
      email: verifiedEmail || propEmail || prev.email,
      mobileNumber: verifiedMobile || propMobileNumber || prev.mobileNumber,
      password: userPassword || propPassword || prev.password
    }));
    
    // Debug info
  }, [propEmail, propMobileNumber, propPassword]);

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>): void => {
    const { name, value } = e.target;
    setUserData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when typing
    if (error) setError("");
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    // Validate required data
    if (!userData.name || !userData.dateOfBirth || !userData.maritalStatus || !userData.place) {
      setError("Name, Date of Birth, Marital Status and Place are required");
      return;
    }
    
    // Validate based on registration type
    if (registrationType === 'email' && !userData.email) {
      setError("Email is required for email registration");
      return;
    }
    
    if (registrationType === 'mobile' && !userData.mobileNumber) {
      setError("Mobile number is required for mobile registration");
      return;
    }
    
    // Ensure we have password
    if (!userData.password) {
      setError("Password is required. Please start the registration process again.");
      return;
    }
    
    setIsLoading(true);
    setDebugInfo(prevInfo => prevInfo + "\nSubmitting registration...");
    
    try {
      // Prepare data for API call - exactly matching the structure the backend expects
      const signupData = {
        name: userData.name,
        password: userData.password,
        dob: userData.dateOfBirth,
        marital_status: userData.maritalStatus,
        place: userData.place,
        user_type: "normal", // Use "normal" as specified in your backend
        // Add email or mobile_number based on registration type
        ...(registrationType === 'email' 
          ? { email: userData.email, mobile_number: userData.mobileNumber || null } 
          : { mobile_number: userData.mobileNumber, email: userData.email || null })
      };
      
      console.log("Sending registration data:", { ...signupData, password: '********' });
      setDebugInfo(prevInfo => prevInfo + `\nRegistration data: ${JSON.stringify({ 
        ...signupData, 
        password: '********' 
      }, null, 2)}`);
      
      // Clean up sessionStorage
      sessionStorage.removeItem('verifiedEmail');
      sessionStorage.removeItem('verifiedMobile');
      sessionStorage.removeItem('userPassword');
      
      // Call the signup API
      const response = await authService.signup(signupData);
      
      setDebugInfo(prevInfo => prevInfo + "\nSignup successful, checking profile...");
      
      // Check if profile is complete
      try {
        await authService.checkProfile();
        setDebugInfo(prevInfo => prevInfo + "\nProfile check successful, redirecting to home...");
        // If successful, redirect to home
        router.push('/home');
      } catch (profileError: any) {
        setDebugInfo(prevInfo => prevInfo + "\nProfile check error, still redirecting to home...");
        // For profile errors, still redirect to home
        router.push('/home');
      }
      
      // If using callback approach, also call this
      if (onComplete) {
        onComplete({
          name: userData.name,
          dateOfBirth: userData.dateOfBirth,
          maritalStatus: userData.maritalStatus,
          place: userData.place
        });
      }
    } catch (err: any) {
      console.error("Registration error:", err);
      setError(err.error || "Registration failed. Please try again.");
      setDebugInfo(prevInfo => prevInfo + `\nRegistration error: ${JSON.stringify(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full px-6 py-6 rounded-lg">
        {/* Back Button */}
        {onBack && (
          <button
            onClick={onBack}
            className="absolute top-4 left-4 text-gray-600 hover:text-gray-800 flex items-center"
            disabled={isLoading}
          >
            <ArrowLeft size={18} />
          </button>
        )}

        {/* Logo */}
        <div className="flex justify-center mb-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        {/* Heading */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">
            Welcome to <span style={{ color: "#B31B1E" }}>Wedzat</span>
          </h1>
          <p className="text-sm mt-1">Register your Information</p>
          {registrationType === 'email' && userData.email && (
            <p className="text-sm mt-1 text-gray-500">
              Registering with email: {userData.email}
            </p>
          )}
          {registrationType === 'mobile' && userData.mobileNumber && (
            <p className="text-sm mt-1 text-gray-500">
              Registering with mobile: {userData.mobileNumber}
            </p>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {error}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <input
              type="text"
              name="name"
              placeholder="Name"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white"
              value={userData.name}
              onChange={handleChange}
              required
              disabled={isLoading}
            />
          </div>
          
          <div>
            <input
              type="date" // This will automatically use YYYY-MM-DD format
              name="dateOfBirth"
              placeholder="YYYY-MM-DD"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white"
              value={userData.dateOfBirth}
              onChange={handleChange}
              required
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500 mt-1">Please enter date in YYYY-MM-DD format</p>
          </div>
          
          {/* Email or Mobile field based on registration type */}
          {registrationType === 'mobile' ? (
            <div>
              <input
                type="email"
                name="email"
                placeholder="Email (optional)"
                className="w-full p-4 border border-gray-200 rounded-lg bg-white"
                value={userData.email}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
          ) : (
            <div>
              <input
                type="tel"
                name="mobileNumber"
                placeholder="Mobile Number (optional)"
                className="w-full p-4 border border-gray-200 rounded-lg bg-white"
                value={userData.mobileNumber}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
          )}
          
          <div className="relative">
            <select
              name="maritalStatus"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white appearance-none"
              value={userData.maritalStatus}
              onChange={handleChange}
              required
              disabled={isLoading}
            >
              <option value="" disabled>Marital Status</option>
              <option value="single">Single</option>
              <option value="married">Married</option>
              <option value="divorced">Divorced</option>
              <option value="widowed">Widowed</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
          
          <div className="relative">
            <select
              name="place"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white appearance-none"
              value={userData.place}
              onChange={handleChange}
              required
              disabled={isLoading}
            >
              <option value="" disabled>Place</option>
              <option value="Delhi">Delhi</option>
              <option value="Mumbai">Mumbai</option>
              <option value="Bangalore">Bangalore</option>
              <option value="Kolkata">Kolkata</option>
              <option value="Chennai">Chennai</option>
              <option value="Hyderabad">Hyderabad</option>
              <option value="Other">Other</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
          
          <button
            type="submit"
            className="w-full bg-red-700 text-white py-4 rounded-md hover:bg-red-800 transition duration-200 mt-6 disabled:bg-red-300"
            disabled={isLoading}
          >
            {isLoading ? "Creating Account..." : "Create Account"}
          </button>
        </form>
      </div>
    </div>
  );
};
export default Registration;