// components/upload/FileSelectionStep.tsx
'use client';

import React, { useState } from 'react';
import { Upload, Camera, Video } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';
import { formatFileSize } from '../../utils/uploadUtils'
const FileSelectionStep: React.FC = () => {
  const { setFile } = useUpload();
  const [isDragging, setIsDragging] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      setFile(e.dataTransfer.files[0]);
    }
  };

  return (
    <div 
      className={`border-2 border-dashed rounded-lg p-10 text-center ${
        isDragging ? 'border-red-500 bg-red-50' : 'border-gray-300'
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <Upload className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-4 text-lg font-medium text-gray-900">
        Drag and drop your media here
      </h3>
      <p className="mt-1 text-sm text-gray-500">
        Or click to browse from your device
      </p>
      
      <div className="mt-6 flex justify-center gap-4">
        <label className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 cursor-pointer">
          <Camera className="mr-2 h-5 w-5" />
          Upload Photo
          <input 
            type="file" 
            className="hidden" 
            onChange={handleFileSelect} 
            accept="image/*" 
          />
        </label>
        
        <label className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 cursor-pointer">
          <Video className="mr-2 h-5 w-5" />
          Upload Video
          <input 
            type="file" 
            className="hidden" 
            onChange={handleFileSelect} 
            accept="video/*" 
          />
        </label>
      </div>
      
      <div className="mt-4 text-xs text-gray-500">
        <p>Supported formats: JPEG, PNG, GIF, MP4, MOV</p>
        <p className="mt-1">
          Photo size limit: Unlimited
        </p>
        <p className="mt-1">
          Video size limit: Unlimited
        </p>
        <p className="mt-1 text-yellow-600">
          Note: Very large files may take longer to upload
        </p>
      </div>
    </div>
  );
};

export default FileSelectionStep;
