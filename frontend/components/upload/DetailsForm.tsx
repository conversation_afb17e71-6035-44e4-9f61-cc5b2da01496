// components/upload/DetailsForm.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Camera, Video, X, Info } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';
import { formatFileSize } from '../../utils/uploadUtils';

// Define the media categories from your backend
const VIDEO_CATEGORIES = ["story", "glimpse", "short", "long"];
const PHOTO_CATEGORIES = ["story", "post"];

const DetailsForm: React.FC = () => {
  const { 
    state, 
    setMediaType, 
    setCategory, 
    setTitle, 
    setDescription, 
    addTag, 
    removeTag, 
    setDetailField, 
    startUpload, 
    resetUpload
  } = useUpload();

  const [tagInput, setTagInput] = useState('');
  const [suggestedTags, setSuggestedTags] = useState<string[]>([]);

  useEffect(() => {
    if (state.file) {
      // Generate suggested tags based on file and category
      const suggestions = getSuggestedTags(state.file, state.category);

      function getSuggestedTags(file: File, category: string): string[] {
        // Example implementation: Replace with your logic
        return category === "photo" ? ["nature", "portrait"] : ["travel", "vlog"];
      }
      setSuggestedTags(suggestions);
    }
  }, [state.file, state.category]);

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagInput(e.target.value);
  };

  const handleAddTag = () => {
    if (tagInput.trim()) {
      addTag(tagInput.trim());
      setTagInput('');
    }
  };

  const handleSuggestedTagClick = (tag: string) => {
    if (!state.tags.includes(tag)) {
      addTag(tag);
    }
  };

  const handleTagKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Render detail fields inputs based on media type
  const renderDetailFields = () => {
    const fields = [
      { key: "location", label: "Location" },
      { key: "event", label: "Event" },
      { key: "date", label: "Date" },
      { key: "time", label: "Time" },
      { key: "people", label: "People" },
      { key: "mood", label: "Mood" },
      state.mediaType === "video" ? { key: "camera", label: "Camera Used" } : null,
      state.mediaType === "video" ? { key: "resolution", label: "Resolution" } : null,
      state.mediaType === "photo" ? { key: "camera", label: "Camera Used" } : null,
      state.mediaType === "photo" ? { key: "dimensions", label: "Dimensions" } : null,
    ].filter(Boolean);

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {fields.map((field) => field && (
          <div key={field.key} className="mb-4">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              {field.label}
            </label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              value={state.detailFields[field.key] || ""}
              onChange={(e) => setDetailField(field.key, e.target.value)}
              placeholder={`Enter ${field.label}`}
            />
          </div>
        ))}
      </div>
    );
  };

    function formatTime(duration: number): React.ReactNode {
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

  return (
    <form onSubmit={(e) => { e.preventDefault(); startUpload(); }}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Media Preview */}
        <div className="lg:col-span-1 border rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center">
          {state.mediaType === "photo" && state.file ? (
            <div className="relative h-64 w-full">
              <Image
                src={URL.createObjectURL(state.file)}
                alt="Preview"
                fill
                sizes="(max-width: 768px) 100vw, 33vw"
                className="object-contain"
              />
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {formatFileSize(state.file.size)}
              </div>
            </div>
          ) : state.mediaType === "video" && state.file ? (
            <div className="relative h-64 w-full">
              <video 
                src={URL.createObjectURL(state.file)} 
                controls 
                className="h-full w-full object-contain"
              />
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {state.duration ? formatTime(state.duration) : ''} • {formatFileSize(state.file.size)}
              </div>
            </div>
          ) : (
            <div className="h-64 w-full flex items-center justify-center text-gray-400">
              No file selected
            </div>
          )}
        </div>
        
        {/* Form Fields */}
        <div className="lg:col-span-2">
          {/* Media Type & Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-2">
                Media Type
              </label>
              <div className="flex items-center space-x-4">
                <button
                  type="button"
                  className={`flex items-center px-3 py-2 rounded-md ${
                    state.mediaType === "photo" 
                      ? "bg-red-100 text-red-700" 
                      : "bg-gray-100 text-gray-700"
                  }`}
                  onClick={() => setMediaType("photo")}
                >
                  <Camera className="h-5 w-5 mr-2" />
                  Photo
                </button>
                <button
                  type="button"
                  className={`flex items-center px-3 py-2 rounded-md ${
                    state.mediaType === "video" 
                      ? "bg-red-100 text-red-700" 
                      : "bg-gray-100 text-gray-700"
                  }`}
                  onClick={() => setMediaType("video")}
                >
                  <Video className="h-5 w-5 mr-2" />
                  Video
                </button>
              </div>
            </div>
            
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-2">
                Category
              </label>
              <select
                value={state.category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                {state.mediaType === "video" 
                  ? VIDEO_CATEGORIES.map(cat => (
                      <option key={cat} value={cat}>{cat.charAt(0).toUpperCase() + cat.slice(1)}</option>
                    ))
                  : PHOTO_CATEGORIES.map(cat => (
                      <option key={cat} value={cat}>{cat.charAt(0).toUpperCase() + cat.slice(1)}</option>
                    ))
                }
              </select>
            </div>
          </div>
          
          {/* Title & Description */}
          <div className="mt-4">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={state.title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Enter a title for your media"
              required
            />
          </div>
          
          <div className="mt-4">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              Description
            </label>
            <textarea
              value={state.description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Describe your media..."
            />
          </div>
          
          {/* Tags */}
          <div className="mt-4">
            <label className="block text-gray-700 text-sm font-medium mb-2">
              Tags
            </label>
            <div className="flex">
              <input
                type="text"
                value={tagInput}
                onChange={handleTagInputChange}
                onKeyPress={handleTagKeyPress}
                className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Add tags (press Enter)"
              />
              <button
                type="button"
                onClick={handleAddTag}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-r-md hover:bg-gray-300"
              >
                Add
              </button>
            </div>
            
            {/* Suggested tags */}
            {suggestedTags.length > 0 && (
              <div className="mt-2">
                <p className="text-xs text-gray-500">Suggested tags:</p>
                <div className="flex flex-wrap mt-1 gap-1">
                  {suggestedTags.map((tag) => (
                    <button
                      key={tag}
                      type="button"
                      onClick={() => handleSuggestedTagClick(tag)}
                      className={`text-xs px-2 py-1 rounded ${
                        state.tags.includes(tag)
                          ? "bg-gray-200 text-gray-500 cursor-default"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      }`}
                      disabled={state.tags.includes(tag)}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            {state.tags.length > 0 && (
              <div className="flex flex-wrap mt-2 gap-2">
                {state.tags.map((tag) => (
                  <span 
                    key={tag} 
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1.5 h-4 w-4 flex items-center justify-center rounded-full text-red-400 hover:bg-red-200 hover:text-red-500 focus:outline-none"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>
          
          {/* Duration (for videos only) */}
          {state.mediaType === "video" && (
            <div className="mt-4">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                Duration
              </label>
              <div className="text-sm text-gray-700">
                {state.duration ? formatTime(state.duration) : 'Unknown duration'}
              </div>
            </div>
          )}
          
          {/* Detail Fields */}
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900">
              Additional Details
            </h3>
            <p className="text-sm text-gray-500 flex items-center">
              <Info className="h-4 w-4 mr-1 text-blue-500" />
              At least 4 detail fields are required
            </p>
            
            {renderDetailFields()}
          </div>
        </div>
      </div>
      
      {/* Action Buttons */}
      <div className="mt-8 flex justify-end space-x-4">
        <button
          type="button"
          onClick={resetUpload}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Upload
        </button>
      </div>
    </form>
  );
};

export default DetailsForm;