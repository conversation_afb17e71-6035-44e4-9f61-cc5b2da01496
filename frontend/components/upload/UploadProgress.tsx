// components/upload/UploadProgress.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { X, Upload, CheckCircle, RefreshCw } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';
import { handleUploadError } from '../../utils/uploadErrorHandler';

interface UploadProgressProps {
  onClose: () => void;
  onGoBack?: () => void;
}

const UploadProgress: React.FC<UploadProgressProps> = ({ onClose, onGoBack }) => {
  const { state, resetUpload, startUpload } = useUpload();
  const [retrying, setRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Format progress as a percentage
  const progressPercentage = Math.min(Math.round(state.progress), 100);
  const isComplete = state.step === 'complete';
  const hasError = state.step === 'error';

  // Reset retrying state when error state changes
  useEffect(() => {
    if (!hasError) {
      setRetrying(false);
    }
  }, [hasError]);

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
      <div className="bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]">
        {/* Close button - only show when complete or error */}
        {(isComplete || hasError) && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-800 hover:text-red-600"
          >
            <X size={24} />
          </button>
        )}

        {/* Header */}
        <div className="flex items-center mb-6">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={32}
              height={32}
              className="object-cover"
            />
          </div>
          <h2 className="text-xl font-bold">
            {hasError ? 'Upload Failed' : isComplete ? 'Upload Complete' : 'Uploading...'}
          </h2>
          <div className="ml-2">
            <Image
              src="/pics/umoments.png"
              alt="Moments"
              width={20}
              height={20}
              className="object-cover"
            />
          </div>
        </div>

        {/* Progress indicator */}
        <div className="mb-8">
          {hasError ? (
            <div className="bg-red-100 text-red-800 p-4 rounded-lg mb-4">
              <p className="font-medium">Error uploading your content</p>
              <p className="text-sm mt-1">{state.error || 'An unknown error occurred'}</p>

              {/* Error type specific guidance */}
              {state.error?.includes('title') && (
                <>
                  <p className="text-sm mt-2 font-medium text-red-700">
                    Please provide a title for your upload
                  </p>
                  <p className="text-sm mt-1">
                    Please go back to the personal details page and provide a title for your upload.
                  </p>
                </>
              )}
              {(state.error?.includes('Network') || state.error?.includes('network')) && (
                <>
                  <p className="text-sm mt-2 font-medium text-red-700">
                    Network error detected
                  </p>
                  <p className="text-sm mt-1">
                    Please check your internet connection and try again. If the problem persists, try using a different network or browser.
                  </p>
                </>
              )}
              {state.error?.includes('CORS') && (
                <>
                  <p className="text-sm mt-2 font-medium text-red-700">
                    Browser connection issue
                  </p>
                  <p className="text-sm mt-1">
                    There was a problem connecting to our servers. Please try again or use a different browser like Chrome.
                  </p>
                </>
              )}
              {state.error?.includes('timeout') && (
                <>
                  <p className="text-sm mt-2 font-medium text-red-700">
                    Upload timed out
                  </p>
                  <p className="text-sm mt-1">
                    The upload took too long to complete. This might be due to a slow internet connection or a very large file.
                  </p>
                </>
              )}
              {state.error?.includes('Permission denied') && (
                <>
                  <p className="text-sm mt-2 font-medium text-red-700">
                    Upload permission error
                  </p>
                  <p className="text-sm mt-1">
                    The upload link may have expired. Please try again.
                  </p>
                </>
              )}
              {state.error?.includes('too large') && (
                <>
                  <p className="text-sm mt-2 font-medium text-red-700">
                    File size limit exceeded
                  </p>
                  <p className="text-sm mt-1">
                    The file you're trying to upload is too large. Please try a smaller file or compress it.
                  </p>
                </>
              )}
            </div>
          ) : isComplete ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="bg-green-100 rounded-full p-4 mb-4">
                <CheckCircle size={48} className="text-green-600" />
              </div>
              <h3 className="text-xl font-medium text-green-800 mb-2">Upload Successful!</h3>
              <p className="text-gray-600 text-center">
                Your content has been uploaded successfully and is now being processed.
                It will be available on your profile shortly.
              </p>
            </div>
          ) : (
            <>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium">Uploading your content...</span>
                <span className="text-sm font-medium">{progressPercentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-red-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
              <div className="mt-4 flex items-center justify-center">
                <Upload className="animate-bounce text-red-600 mr-2" size={20} />
                <p className="text-gray-600">
                  {progressPercentage < 20 && "Preparing your files..."}
                  {progressPercentage >= 20 && progressPercentage < 70 && "Uploading main content..."}
                  {progressPercentage >= 70 && progressPercentage < 90 && "Uploading thumbnail..."}
                  {progressPercentage >= 90 && "Finalizing your upload..."}
                </p>
              </div>
            </>
          )}
        </div>

        {/* Action buttons - only show when complete or error */}
        {(isComplete || hasError) && (
          <div className="flex justify-center gap-4">
            {hasError && (
              <button
                onClick={onGoBack || onClose} // Go back to personal details if available, otherwise just close
                className="px-6 py-2 border border-red-600 text-red-600 rounded-md hover:bg-red-50 transition duration-200"
              >
                Go Back
              </button>
            )}
            <button
              onClick={hasError ? async () => {
                // If it's a network error, try to retry the upload automatically
                if (state.error?.includes('Network') || state.error?.includes('network') ||
                    state.error?.includes('CORS') || state.error?.includes('timeout')) {
                  setRetrying(true);
                  setRetryCount(prev => prev + 1);
                  try {
                    await startUpload();
                  } catch (error) {
                    console.error('Retry failed:', error);
                  } finally {
                    setRetrying(false);
                  }
                } else {
                  onClose();
                }
              } : onClose}
              className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition duration-200 flex items-center justify-center"
              disabled={retrying}
            >
              {hasError ? (
                retrying ? (
                  <>
                    <RefreshCw className="animate-spin mr-2" size={16} />
                    Retrying...
                  </>
                ) : (
                  'Try Again'
                )
              ) : (
                'Done'
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadProgress;