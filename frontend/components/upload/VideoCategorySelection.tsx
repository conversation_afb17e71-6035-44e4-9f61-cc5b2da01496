// components/upload/VideoCategorySelection.tsx
// This component is used for both video and photo category selection
'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { X, Clock, Image as ImageIcon, Upload } from 'lucide-react';

interface VideoCategorySelectionProps {
  onNext: (category: string) => void;
  onBack: () => void;
  onUpload: (category: string) => void;
  onThumbnailUpload: () => void;
  onClose: () => void;
  mediaType?: 'photo' | 'video'; // Optional prop to specify if this is for photos or videos
  selectedType?: string; // Optional prop to specify the selected type (moments, flashes, etc.)
}

const VideoCategorySelection: React.FC<VideoCategorySelectionProps> = ({
  onNext,
  onBack,
  onUpload,
  onThumbnailUpload,
  onClose,
  mediaType = 'video', // Default to video if not specified
  selectedType = '' // Default to empty string if not specified
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleCategorySelect = (category: string) => {
    console.log('VIDEO CATEGORY SELECTION - Selected category:', category);
    setSelectedCategory(category);

    // Just set the selected category, don't automatically proceed
    // This allows the user to click the Upload button
  };

  const handleNext = () => {
    if (selectedCategory) {
      onNext(selectedCategory);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-2 sm:p-4 bg-black/50 backdrop-blur-sm">
      <div className="bg-[#FFF7E8] rounded-2xl w-full max-w-[95%] sm:max-w-[90%] md:max-w-[80%] lg:max-w-[60%] xl:max-w-[50%] p-3 sm:p-6 relative overflow-y-auto max-h-[90vh]">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-2 sm:top-4 right-2 sm:right-4 text-gray-800 hover:text-red-600"
        >
          <X size={20} className="sm:w-6 sm:h-6" />
        </button>

        {/* Header */}
        <div className="flex items-center mb-3 sm:mb-6">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={28}
              height={28}
              className="object-cover sm:w-8 sm:h-8"
            />
          </div>
          <h2 className="text-lg sm:text-xl font-bold">Create New</h2>
          <div className="ml-2">
            <Image
              src="/pics/umoments.png"
              alt="Moments"
              width={18}
              height={18}
              className="object-cover sm:w-5 sm:h-5"
            />
          </div>
        </div>

        <p className="text-sm sm:text-base mb-3 sm:mb-6">Choose {selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'Photo' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'Video' : selectedType === 'moments' ? 'Moments' : 'Media'} Category</p>

        {/* Add warning for moments videos */}
        {selectedType === 'moments' && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-2 sm:p-4 mb-3 sm:mb-6 text-xs sm:text-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-2 sm:ml-3">
                <p className="text-yellow-700">
                  <strong>Important:</strong> Moments videos must be 1 minute or less in duration. Longer videos will be rejected.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-6">
          {/* Left side - Video category options */}
          <div className="flex flex-col space-y-2 sm:space-y-4 mb-3 sm:mb-6 flex-grow">
            <div
              className={`flex items-center p-2 sm:p-4 border rounded-lg cursor-pointer
                ${selectedCategory === 'my_wedding_videos' ? 'border-red-600' : 'border-gray-200'}
              `}
              onClick={() => handleCategorySelect('my_wedding_videos')}
            >
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-2 sm:mr-3 flex items-center justify-center
                ${selectedCategory === 'my_wedding_videos' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedCategory === 'my_wedding_videos' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-sm sm:text-base font-medium">My Wedding {selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'Photos' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'Videos' : selectedType === 'moments' ? 'Moments' : 'Media'}</span>
            </div>

            <div
              className={`flex items-center p-2 sm:p-4 border rounded-lg cursor-pointer
                ${selectedCategory === 'wedding_influencer' ? 'border-red-600' : 'border-gray-200'}
              `}
              onClick={() => handleCategorySelect('wedding_influencer')}
            >
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-2 sm:mr-3 flex items-center justify-center
                ${selectedCategory === 'wedding_influencer' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedCategory === 'wedding_influencer' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-sm sm:text-base font-medium">Wedding Influencer</span>
            </div>

            <div
              className={`flex items-center p-2 sm:p-4 border rounded-lg cursor-pointer
                ${selectedCategory === 'friends_family_videos' ? 'border-red-600' : 'border-gray-200'}
              `}
              onClick={() => handleCategorySelect('friends_family_videos')}
            >
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-2 sm:mr-3 flex items-center justify-center
                ${selectedCategory === 'friends_family_videos' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedCategory === 'friends_family_videos' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-sm sm:text-base font-medium">Friends / Family {selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'Photos' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'Videos' : selectedType === 'moments' ? 'Moments' : 'Media'}</span>
            </div>
          </div>

          {/* Right side - Upload buttons */}
          <div className="flex flex-col space-y-2 sm:space-y-4">
            <button
              onClick={() => {
                if (selectedCategory) {
                  console.log('VIDEO CATEGORY SELECTION - Upload button clicked with category:', selectedCategory);
                  // Only call onNext to set the category and proceed to file upload
                  // This will trigger handleCategorySelected in the parent which will call handleFileUpload
                  onNext(selectedCategory);
                } else {
                  alert(`Please select a ${selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'photo' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'video' : selectedType === 'moments' ? 'moments' : 'media'} category first`);
                }
              }}
              disabled={!selectedCategory}
              className={`flex flex-col items-center justify-center px-4 sm:px-6 py-3 sm:py-4 rounded-md w-full sm:w-48 h-14 sm:h-16 ${selectedCategory ? 'bg-black text-white hover:bg-gray-800' : 'bg-gray-300 text-gray-500 cursor-not-allowed'} transition duration-200`}
            >
              {selectedType === 'moments' ? (
                <Upload size={18} className="mb-1 sm:mb-1 sm:w-5 sm:h-5" />
              ) : selectedType === 'photos' || mediaType === 'photo' ? (
                <ImageIcon size={18} className="mb-1 sm:mb-1 sm:w-5 sm:h-5" />
              ) : ['flashes', 'glimpses', 'movies'].includes(selectedType) || mediaType === 'video' ? (
                <Clock size={18} className="mb-1 sm:mb-1 sm:w-5 sm:h-5" />
              ) : (
                <ImageIcon size={18} className="mb-1 sm:mb-1 sm:w-5 sm:h-5" />
              )}
              <span className="text-sm sm:text-base">
                {selectedType === 'moments' ? 'Upload Files' :
                  selectedType === 'photos' ? 'Upload Photos' :
                    ['flashes', 'glimpses', 'movies'].includes(selectedType) ? 'Upload Videos' :
                      mediaType === 'photo' ? 'Upload Photos' : 'Upload Videos'}
              </span>
            </button>

            {/* Thumbnail upload button removed */}
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-4 sm:mt-8">
          <button
            onClick={onBack}
            className="flex items-center justify-center px-3 sm:px-6 py-1.5 sm:py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm sm:text-base"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 sm:h-5 sm:w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back
          </button>

          <button
            onClick={handleNext}
            disabled={!selectedCategory}
            className={`flex items-center justify-center px-3 sm:px-6 py-1.5 sm:py-2 rounded-md text-sm sm:text-base ${selectedCategory
              ? 'bg-red-600 text-white hover:bg-red-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              } transition duration-200`}
          >
            Next
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 sm:h-5 sm:w-5 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default VideoCategorySelection;