// components/upload/ThumbnailSelection.tsx
'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { X, Upload, Check } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';

interface ThumbnailSelectionProps {
  videoFile: File;
  onNext: (thumbnailFile?: File) => void;
  onBack: () => void;
  onClose: () => void;
}

const ThumbnailSelection: React.FC<ThumbnailSelectionProps> = ({
  videoFile,
  onNext,
  onBack,
  onClose
}) => {
  const { setDuration } = useUpload();
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  const [videoThumbnails, setVideoThumbnails] = useState<string[]>([]);
  const [selectedThumbnail, setSelectedThumbnail] = useState<number | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Generate thumbnails when the component mounts or when videoFile changes
    if (videoFile) {
      console.log('ThumbnailSelection: Processing video file:', videoFile.name);

      // Check if the file is actually a video
      if (!videoFile.type.startsWith('video/')) {
        console.error('ThumbnailSelection: File is not a video:', videoFile.type);
        alert('Error: The selected file is not a video. Please go back and select a video file.');
        generateDefaultThumbnail();
        setIsGenerating(false);
        return;
      }

      // Clear any existing thumbnails
      setVideoThumbnails([]);
      setSelectedThumbnail(null);
      setThumbnailPreview(null);
      setThumbnailFile(null);

      try {
        generateVideoThumbnails();
      } catch (error) {
        console.error('Error generating thumbnails:', error);
        generateDefaultThumbnail();
        setIsGenerating(false);
      }
    } else {
      console.error('ThumbnailSelection: No video file provided');
      // Generate a default thumbnail instead of showing an error
      generateDefaultThumbnail();
      setIsGenerating(false);
    }
  }, [videoFile]);

  const generateVideoThumbnails = () => {
    console.log('Generating video thumbnails');
    setIsGenerating(true);

    // Check if videoFile exists
    if (!videoFile) {
      console.error('No video file provided to generateVideoThumbnails');
      generateDefaultThumbnail();
      return;
    }

    try {
      // Create a video element to load the video
      const video = document.createElement('video');
      video.preload = 'metadata';

      // Create object URL for the video file
      const videoUrl = URL.createObjectURL(videoFile);
      video.src = videoUrl;

      // Set a timeout to handle cases where the video doesn't load
      const timeoutId = setTimeout(() => {
        console.error('Timeout waiting for video to load');
        setIsGenerating(false);
        URL.revokeObjectURL(videoUrl);
        // Generate a default thumbnail if we can't load the video
        generateDefaultThumbnail();
      }, 10000); // 10 second timeout

      video.onloadedmetadata = () => {
        clearTimeout(timeoutId);

        // Get video duration
        const duration = video.duration;

        // Set the duration in the upload context
        setDuration(duration);
        console.log(`Video duration set to ${duration} seconds`);

        // Generate 3 thumbnails at different points in the video
        const thumbnailPoints = [
          duration * 0.25, // 25% of the way through
          duration * 0.5,  // 50% of the way through
          duration * 0.75  // 75% of the way through
        ];

        const thumbnails: string[] = [];
        let loadedCount = 0;

        // Set a timeout for each thumbnail generation
        const seekTimeoutId = setTimeout(() => {
          // Instead of warning, just log that we're using a fallback approach
          console.log('Using fallback approach for thumbnail generation');
          if (thumbnails.length > 0) {
            // Use whatever thumbnails we have
            setVideoThumbnails(thumbnails);
          } else {
            // Generate a default thumbnail
            generateDefaultThumbnail();
          }
          setIsGenerating(false);
          URL.revokeObjectURL(videoUrl);
        }, 8000); // Increase timeout to 8 seconds for slower devices

        thumbnailPoints.forEach((time, index) => {
          try {
            // Set the current time of the video
            video.currentTime = time;

            // When the time updates, capture the frame
            video.onseeked = () => {
              try {
                // Create a canvas to draw the video frame
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth || 320;
                canvas.height = video.videoHeight || 240;

                // Draw the video frame on the canvas
                const ctx = canvas.getContext('2d');
                if (ctx) {
                  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                  // Convert canvas to data URL
                  const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.7);
                  thumbnails[index] = thumbnailUrl;

                  loadedCount++;

                  // When all thumbnails are generated
                  if (loadedCount === thumbnailPoints.length) {
                    clearTimeout(seekTimeoutId);
                    setVideoThumbnails(thumbnails);
                    setIsGenerating(false);

                    // Revoke the object URL to free memory
                    URL.revokeObjectURL(videoUrl);
                  }
                }
              } catch (error) {
                console.error('Error capturing video frame:', error);
                // If we fail to capture a frame, still try to use what we have
                if (thumbnails.length === 0) {
                  generateDefaultThumbnail();
                } else if (loadedCount === thumbnailPoints.length - 1) {
                  // If this was the last thumbnail and it failed, use what we have
                  setVideoThumbnails(thumbnails);
                  setIsGenerating(false);
                  URL.revokeObjectURL(videoUrl);
                }
              }
            };
          } catch (error) {
            console.error('Error seeking video:', error);
            loadedCount++; // Count this as processed even though it failed

            // If all thumbnails have been attempted (even with errors)
            if (loadedCount === thumbnailPoints.length) {
              if (thumbnails.length > 0) {
                setVideoThumbnails(thumbnails);
              } else {
                generateDefaultThumbnail();
              }
              setIsGenerating(false);
              URL.revokeObjectURL(videoUrl);
            }
          }
        });
      };

      video.onerror = (e) => {
        clearTimeout(timeoutId);
        console.error('Error loading video for thumbnail generation:', e);
        setIsGenerating(false);
        URL.revokeObjectURL(videoUrl);
        // Generate a default thumbnail
        generateDefaultThumbnail();
      };
    } catch (error) {
      console.error('Error in thumbnail generation:', error);
      setIsGenerating(false);
      // Generate a default thumbnail
      generateDefaultThumbnail();
    }
  };

  // Generate a default thumbnail if we can't extract from the video
  const generateDefaultThumbnail = () => {
    try {
      // Create a canvas with a placeholder image
      const canvas = document.createElement('canvas');
      canvas.width = 320;
      canvas.height = 240;

      const ctx = canvas.getContext('2d');
      if (ctx) {
        // Fill with a gradient background
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#f0f0f0');
        gradient.addColorStop(1, '#d0d0d0');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add text
        ctx.fillStyle = '#333';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Video Preview', canvas.width / 2, canvas.height / 2);

        // Add file name if available
        if (videoFile) {
          ctx.font = '14px Arial';
          let fileName = videoFile.name;
          if (fileName.length > 25) {
            fileName = fileName.substring(0, 22) + '...';
          }
          ctx.fillText(fileName, canvas.width / 2, canvas.height / 2 + 30);
        } else {
          ctx.font = '14px Arial';
          ctx.fillText('No file selected', canvas.width / 2, canvas.height / 2 + 30);
        }

        // Convert to data URL
        const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.8);
        setVideoThumbnails([thumbnailUrl]);
        setIsGenerating(false); // Make sure we're not stuck in generating state
      }
    } catch (error) {
      console.error('Error generating default thumbnail:', error);
      setIsGenerating(false); // Make sure we're not stuck in generating state
      // Create a simple fallback thumbnail
      setVideoThumbnails(['data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==']);
    }
  };

  const handleThumbnailUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Check if the file is an image
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file for the thumbnail.');
        return;
      }

      setThumbnailFile(file);

      // Create a preview of the uploaded thumbnail
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setThumbnailPreview(e.target.result as string);
          setSelectedThumbnail(null); // Deselect any auto-generated thumbnails
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSelectThumbnail = (index: number) => {
    setSelectedThumbnail(index);
    setThumbnailPreview(null); // Clear any uploaded thumbnail preview
    setThumbnailFile(null);    // Clear any uploaded thumbnail file
  };

  const handleNext = async () => {
    console.log('ThumbnailSelection: handleNext called');
    try {
      if (thumbnailFile) {
        // User uploaded a custom thumbnail
        console.log('Using user-uploaded thumbnail');
        onNext(thumbnailFile);
      } else if (selectedThumbnail !== null && videoThumbnails[selectedThumbnail]) {
        // User selected an auto-generated thumbnail
        // Convert the data URL to a File object
        console.log('Using selected auto-generated thumbnail');
        try {
          const dataUrl = videoThumbnails[selectedThumbnail];
          const res = await fetch(dataUrl);
          const blob = await res.blob();
          const file = new File([blob], `thumbnail-${Date.now()}.jpg`, { type: 'image/jpeg' });
          onNext(file);
        } catch (error) {
          console.error('Error converting selected thumbnail to file:', error);
          // Try to use the first thumbnail as fallback
          if (videoThumbnails.length > 0 && selectedThumbnail !== 0) {
            try {
              console.log('Using fallback thumbnail');
              const dataUrl = videoThumbnails[0];
              const res = await fetch(dataUrl);
              const blob = await res.blob();
              const file = new File([blob], `thumbnail-fallback-${Date.now()}.jpg`, { type: 'image/jpeg' });
              onNext(file);
              return;
            } catch (innerError) {
              console.error('Error converting fallback thumbnail to file:', innerError);
              console.log('Proceeding without thumbnail due to fallback error');
            }
          }
          console.log('Proceeding without thumbnail');
          onNext(); // Proceed without thumbnail
        }
      } else {
        // No thumbnail selected, use the first auto-generated one if available
        if (videoThumbnails.length > 0) {
          try {
            console.log('Using first auto-generated thumbnail');
            const dataUrl = videoThumbnails[0];
            const res = await fetch(dataUrl);
            const blob = await res.blob();
            const file = new File([blob], `thumbnail-auto-${Date.now()}.jpg`, { type: 'image/jpeg' });
            onNext(file);
          } catch (error) {
            console.error('Error converting auto thumbnail to file:', error);
            console.log('Proceeding without thumbnail due to auto-thumbnail error');
            onNext(); // Proceed without thumbnail
          }
        } else {
          // No thumbnails available
          console.log('No thumbnails available, proceeding without thumbnail');
          onNext();
        }
      }
    } catch (error) {
      console.error('Unexpected error in handleNext:', error);
      console.log('Proceeding without thumbnail due to unexpected error');
      onNext(); // Proceed without thumbnail in case of any error
    }
  };

  // If there's no video file, show a message but don't break the flow
  useEffect(() => {
    if (!videoFile) {
      console.error('ThumbnailSelection: No video file provided');
      // Generate a default thumbnail instead of showing an error
      generateDefaultThumbnail();
      setIsGenerating(false);
    }
  }, []);

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
      <div className="bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-800 hover:text-red-600"
        >
          <X size={24} />
        </button>

        {/* Header */}
        <div className="flex items-center mb-6">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={32}
              height={32}
              className="object-cover"
            />
          </div>
          <h2 className="text-xl font-bold">Choose Thumbnail</h2>
          <div className="ml-2">
            <Image
              src="/pics/umoments.png"
              alt="Moments"
              width={20}
              height={20}
              className="object-cover"
            />
          </div>
        </div>

        <p className="text-base mb-6">Select a thumbnail for your video or upload your own</p>

        {/* Thumbnail options */}
        <div className="mb-6">
          {isGenerating ? (
            <div className="flex justify-center items-center h-40">
              <p>Generating thumbnails from your video...</p>
            </div>
          ) : !videoFile ? (
            <div className="flex justify-center items-center h-40 bg-gray-100 rounded-lg">
              <div className="text-center">
                <p className="text-gray-600 mb-2">No video file available</p>
                <button
                  onClick={onBack}
                  className="px-4 py-2 bg-red-600 text-white rounded-md"
                >
                  Go Back to Select Video
                </button>
              </div>
            </div>
          ) : (
            <div>
              {/* Auto-generated thumbnails */}
              {videoThumbnails.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-lg font-medium mb-2">Auto-generated thumbnails</h3>
                  <div className="grid grid-cols-3 gap-4">
                    {videoThumbnails.map((thumbnail, index) => (
                      <div
                        key={index}
                        className={`relative cursor-pointer border-2 rounded-lg overflow-hidden ${
                          selectedThumbnail === index ? 'border-red-600' : 'border-gray-200'
                        }`}
                        onClick={() => handleSelectThumbnail(index)}
                      >
                        <img
                          src={thumbnail}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-32 object-cover"
                        />
                        {selectedThumbnail === index && (
                          <div className="absolute top-2 right-2 bg-red-600 rounded-full p-1">
                            <Check size={16} className="text-white" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Custom thumbnail upload */}
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Upload custom thumbnail</h3>
                <div className="flex items-center">
                  {thumbnailPreview ? (
                    <div className="relative mr-4 border-2 border-red-600 rounded-lg overflow-hidden">
                      <img
                        src={thumbnailPreview}
                        alt="Custom thumbnail"
                        className="w-32 h-32 object-cover"
                      />
                      <div className="absolute top-2 right-2 bg-red-600 rounded-full p-1">
                        <Check size={16} className="text-white" />
                      </div>
                    </div>
                  ) : null}

                  <button
                    onClick={handleThumbnailUpload}
                    className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    <Upload size={18} className="mr-2" />
                    Upload Image
                  </button>

                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="image/*"
                    className="hidden"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <button
            onClick={onBack}
            className="flex items-center justify-center px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back
          </button>

          <button
            onClick={handleNext}
            className="flex items-center justify-center px-6 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 transition duration-200"
          >
            Next
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThumbnailSelection;
