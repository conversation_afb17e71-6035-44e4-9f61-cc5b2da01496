import React, { useEffect } from 'react';

const BotpressScripts: React.FC = () => {
  useEffect(() => {
    // Add Botpress scripts to the document
    const injectScript = document.createElement('script');
    injectScript.src = 'https://cdn.botpress.cloud/webchat/v2.3/inject.js';
    injectScript.async = true;
    document.head.appendChild(injectScript);

    const configScript = document.createElement('script');
    configScript.src = 'https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js';
    configScript.async = true;
    document.head.appendChild(configScript);

    // Cleanup function to remove scripts when component unmounts
    return () => {
      document.head.removeChild(injectScript);
      document.head.removeChild(configScript);
    };
  }, []);

  return null; // This component doesn't render anything
};

export default BotpressScripts;
