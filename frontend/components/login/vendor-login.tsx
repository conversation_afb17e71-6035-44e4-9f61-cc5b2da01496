// components/login/vendor-login.tsx
import React, { useState, ChangeEvent, FormEvent } from "react";
import Image from "next/image";
import { ArrowLeft, Eye, EyeOff } from "lucide-react";
import { useRouter } from 'next/navigation';
import { authService, userService } from "../../services/api"; // Import both services

interface VendorLoginProps {
  onLogin?: (credentials: { email: string; password: string }) => void;
  onForgotPassword?: () => void;
  onSignupClick?: () => void;
  onBack?: () => void;
}

const VendorLogin: React.FC<VendorLoginProps> = ({
  onLogin,
  onForgotPassword,
  onSignupClick,
  onBack,
}) => {
  const router = useRouter();
  const [credentials, setCredentials] = useState({
    email: "",
    password: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({
    email: "",
    password: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [serverError, setServerError] = useState("");

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setCredentials(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when typing
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }

    // Clear server error when typing
    if (serverError) {
      setServerError("");
    }
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const newErrors = { email: "", password: "" };

    // Email validation
    if (!credentials.email) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(credentials.email)) {
      newErrors.email = "Please enter a valid email";
      isValid = false;
    }

    // Password validation
    if (!credentials.password) {
      newErrors.password = "Password is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: FormEvent): Promise<void> => {
    e.preventDefault();

    if (validateForm()) {
      setIsLoading(true);

      try {
        // Call the login API with vendor user type
        await authService.login({
          email: credentials.email,
          password: credentials.password
        });

        // Mark this as a vendor login
        localStorage.setItem('is_vendor', 'true');

        // Also set as cookie for middleware
        document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';

        // Get user details to verify this is a vendor account
        const userDetails = await userService.getUserDetails(); // Using userService now

        // Check if this is a vendor account
        if (userDetails.user_type !== 'vendor') {
          setServerError("This account is not registered as a vendor. Please use the appropriate login method.");
          authService.logout(); // Log out as this is not a vendor account
          localStorage.removeItem('is_vendor'); // Remove vendor flag
          setIsLoading(false);
          return;
        }

        // Check if profile is complete
        try {
          await authService.checkProfile();
          // If successful, redirect to vendor dashboard
          router.push('/vendor/dashboard');
        } catch (profileError: any) {
          // If profile is incomplete, redirect to profile update page
          router.push('/vendor/profile/update');
        }

        // If we're using the callback approach, also call this
        if (onLogin) {
          onLogin(credentials);
        }
      } catch (error: any) {
        console.error("Vendor login error:", error);
        setServerError(error.error || "Invalid credentials. Please try again.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const togglePasswordVisibility = (): void => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full px-6 py-6 rounded-lg">
        {/* Back Button */}
        {onBack && (
          <button
            onClick={onBack}
            className="absolute top-4 left-4 text-gray-600 hover:text-gray-800 flex items-center"
          >
            <ArrowLeft size={18} />
          </button>
        )}

        {/* Logo */}
        <div className="flex justify-center mb-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        {/* Heading */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">
            Vendor Login
          </h1>
          <p className="text-sm mt-1 text-gray-600">
            Welcome back! Please login to continue
          </p>
        </div>

        {/* Server Error */}
        {serverError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {serverError}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email Field */}
          <div>
            <div className="mb-1">
              <label htmlFor="email" className="text-sm font-medium text-gray-700">
                Email Address
              </label>
            </div>
            <input
              id="email"
              type="email"
              name="email"
              placeholder="Enter your email"
              className={`w-full p-3 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
              value={credentials.email}
              onChange={handleChange}
              disabled={isLoading}
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email}</p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <div className="flex justify-between mb-1">
              <label htmlFor="password" className="text-sm font-medium text-gray-700">
                Password
              </label>
              {onForgotPassword && (
                <button
                  type="button"
                  onClick={onForgotPassword}
                  className="text-sm text-red-700 hover:text-red-800"
                  disabled={isLoading}
                >
                  Forgot password?
                </button>
              )}
            </div>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                name="password"
                placeholder="Enter your password"
                className={`w-full p-3 border ${errors.password ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                value={credentials.password}
                onChange={handleChange}
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password}</p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6 disabled:bg-red-300"
            disabled={isLoading}
          >
            {isLoading ? "Logging in..." : "Login"}
          </button>
        </form>

        {/* Signup Link */}
        <div className="text-center mt-6">
          <p className="text-gray-700 text-sm">
            Not registered yet?
            <button
              type="button"
              onClick={onSignupClick}
              className="text-red-700 font-medium ml-1 hover:text-red-800"
            >
              Create an account
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default VendorLogin;