"use client";
import React, { useState, useEffect, useCallback } from "react";
import { User, Building, X, Phone, Mail } from "lucide-react";
import Image from "next/image";
import {
  useAuth,
  useSignIn,
  useUser,
  SignOutButton,
  useSignUp,
} from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { OAuthStrategy } from "@clerk/types";
import axios from "axios";

// Declare global Clerk type
declare global {
  interface Window {
    Clerk: {
      session: {
        getToken: (options?: { template?: string }) => Promise<string>;
      };
    };
  }
}

import Login from "./login/login";
import EmailLogin from "./login/email-login";
import OtpVerification from "./verify-otp/verify-otp";
import EmailVerification from "./verify-otp/email-verification";
import Registration from "./registration/registration";
import VendorLogin from "./login/vendor-login";
import VendorSignup from "./registration/vendor-signup";
import UserLogin from "./login/user-login";

type SocialProvider = "Google" | "Apple" | "Facebook" | "Gmail";
type ViewType =
  | "login"
  | "emailLogin"
  | "otp"
  | "userLogin"
  | "emailVerification"
  | "registration"
  | "vendorLogin"
  | "vendorSignup";
type AuthMethodType = "mobile" | "email";

const WedzatLoginPage = () => {
  const [loginPopUp, setLoginPopUp] = useState(false);
  const [currentView, setCurrentView] = useState<ViewType>("login");
  const [authMethod, setAuthMethod] = useState<AuthMethodType>("mobile");
  const [mobileNumber, setMobileNumber] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  // We're using isAuthLoaded directly from useAuth() hook
  const router = useRouter();
  const { isLoaded: isSignUpLoaded, signUp } = useSignUp();
  const { isLoaded: isSignInLoaded, signIn } = useSignIn();
  const [logoStyle, setLogoStyle] = useState({
    filter:
      "brightness(0) saturate(100%) invert(13%) sepia(77%) saturate(4720%) hue-rotate(352deg) brightness(89%) contrast(91%)",
    opacity: 1,
  });
  const [currentFrame, setCurrentFrame] = useState(0);
  const [sequenceComplete, setSequenceComplete] = useState(false);
  // Define the SVG frames array
  const logoFrames = React.useMemo(
    () => [
      "/pics/Property 1=Variant4.png",
      "/pics/Property 1=Variant3.png",
      "/pics/Property 1=Variant2.png",
      "/pics/image.png",
    ],
    []
  );
  // const { isLoaded: isSignInLoaded, signIn } = useSignIn();
  const { isSignedIn, user } = useUser();
  const { isLoaded: isAuthLoaded } = useAuth();

  // Logo animation effect alternating between violet and gold
  useEffect(() => {
    if (sequenceComplete) return; // Don't run if sequence is already complete

    const frameTransitionTime = 400; // 1.8 seconds per transition between frames
    const initialDelay = 0; // 1 second delay before starting animation
    const finalFrameHoldTime = 500; // Hold final frame for 2 seconds before starting color animation

    const timeoutIds: NodeJS.Timeout[] = [];

    // Schedule each frame transition with increasing delays
    logoFrames.forEach((_, index) => {
      const delay = initialDelay + index * frameTransitionTime;
      const timeoutId = setTimeout(() => {
        setCurrentFrame(index);

        // If this is the last frame, mark sequence as complete after a hold time
        if (index === logoFrames.length - 1) {
          const completeTimeoutId = setTimeout(() => {
            setSequenceComplete(true);
          }, finalFrameHoldTime);

          timeoutIds.push(completeTimeoutId);
        }
      }, delay);

      timeoutIds.push(timeoutId);
    });

    // Cleanup function
    return () => {
      timeoutIds.forEach((id) => clearTimeout(id));
    };
  }, [logoFrames, sequenceComplete]);

  useEffect(() => {
    if (!sequenceComplete) return; // Only run after frame sequence completes

    // Define colors to transition between
    const colors = [
      {
        filter:
          "brightness(0) saturate(100%) invert(13%) sepia(77%) saturate(4720%) hue-rotate(352deg) brightness(89%) contrast(91%)",
        opacity: 1,
      }, // #b31b1e (red)
      {
        filter:
          "brightness(0) saturate(100%) invert(18%) sepia(61%) saturate(2848%) hue-rotate(194deg) brightness(94%) contrast(101%)",
        opacity: 1,
      }, // #095387 (dark blue)
    ];

    let colorIndex = 0;
    const colorTransitionTime = 3000; // 3 seconds between color changes

    // Start color transition interval
    const intervalId = setInterval(() => {
      colorIndex = (colorIndex + 1) % colors.length;
      setLogoStyle(colors[colorIndex]);
    }, colorTransitionTime);

    // Cleanup function
    return () => {
      clearInterval(intervalId);
    };
  }, [sequenceComplete]);

  const closePopup = useCallback(() => {
    setLoginPopUp(false);
    setCurrentView("login");
    setAuthMethod("mobile");
  }, []);

  // Removed unused function

  // Auth method switching
  const switchAuthMethod = (method: AuthMethodType) => {
    setAuthMethod(method);
    setCurrentView(method === "mobile" ? "login" : "emailLogin");
  };

  // Mobile auth handlers
  const handleOtpRequest = (mobile: string) => {
    console.log("OTP requested for:", mobile);
    setMobileNumber(mobile);
    setCurrentView("otp");
  };

  const handleOtpVerify = (otp: string) => {
    console.log("OTP verified:", otp);
    setCurrentView("registration");
  };

  const handleResendOtp = () => {
    console.log("Resending OTP to:", mobileNumber);
    // Implement resend OTP logic
  };

  // Email auth handlers
  const handleEmailVerify = (emailAddress: string) => {
    console.log("Verification requested for email:", emailAddress);
    setEmail(emailAddress);
    setCurrentView("emailVerification");
  };

  const handleEmailCodeVerify = (code: string) => {
    console.log("Email verification code verified:", code);
    setCurrentView("registration");
  };

  const handleResendEmailCode = () => {
    console.log("Resending verification code to:", email);
    // Implement resend email code logic
  };

  // Vendor auth handlers
  const handleVendorLogin = (credentials: {
    email: string;
    password: string;
  }) => {
    console.log("Vendor login:", credentials);
    // Implement vendor login logic
    closePopup();
  };

  const handleVendorSignup = (data: Record<string, unknown>) => {
    console.log("Vendor signup:", data);
    // Implement vendor signup logic
    closePopup();
  };

  const handleForgotPassword = () => {
    console.log("Forgot password");
    // Implement forgot password logic
  };

  // Navigation handlers
  const navigateToVendorLogin = () => {
    setCurrentView("vendorLogin");
  };

  const navigateToVendorSignup = () => {
    setCurrentView("vendorSignup");
  };
  const handleSocialSignin = async (provider: SocialProvider) => {
    if (!isSignUpLoaded || !isSignInLoaded) {
      console.error("Sign-in is not loaded yet");
      return;
    }

    try {
      // Map your app's provider names to Clerk's OAuthStrategy type
      const strategyMap: Record<SocialProvider, OAuthStrategy> = {
        Google: "oauth_google",
        Apple: "oauth_apple",
        Facebook: "oauth_facebook",
        Gmail: "oauth_google", // Gmail is handled via Google OAuth
      };

      const strategy = strategyMap[provider];
      console.log(`Starting ${provider} authentication...`);

      // For Google OAuth, we need to handle both sign-in and sign-up in one flow
      // We'll add a special parameter to the URL to help our callback page identify new accounts
      const redirectUrl =
        "/auth/callback?source=social&provider=" + provider.toLowerCase();

      console.log(
        `Authenticating with ${provider} using redirect URL: ${redirectUrl}`
      );

      // Use a single authentication call with the custom redirect URL
      await signIn?.authenticateWithRedirect({
        strategy,
        redirectUrl: redirectUrl,
        redirectUrlComplete: redirectUrl,
      });
    } catch (error) {
      console.error(`Error during ${provider} authentication:`, error);
    }
  };
  // Updated Social Login handler using Clerk
  const handleSocialLogin = async (provider: SocialProvider) => {
    if (!isSignUpLoaded || !isSignInLoaded) {
      console.error("Sign-up is not loaded yet");
      return;
    }

    try {
      // Map your app's provider names to Clerk's OAuthStrategy type
      const strategyMap: Record<SocialProvider, OAuthStrategy> = {
        Google: "oauth_google",
        Apple: "oauth_apple",
        Facebook: "oauth_facebook",
        Gmail: "oauth_google", // Gmail is handled via Google OAuth
      };

      const strategy = strategyMap[provider];
      console.log(`Starting ${provider} authentication...`);

      // For Google OAuth, we need to handle both sign-in and sign-up in one flow
      // We'll add a special parameter to the URL to help our callback page identify new accounts
      const redirectUrl =
        "/auth/callback?source=social&provider=" + provider.toLowerCase();

      console.log(
        `Authenticating with ${provider} using redirect URL: ${redirectUrl}`
      );

      // Use a single authentication call with the custom redirect URL
      await signUp?.authenticateWithRedirect({
        strategy,
        redirectUrl: redirectUrl,
        redirectUrlComplete: redirectUrl,
      });
    } catch (error) {
      console.error(`Error during ${provider} authentication:`, error);
    }
  };

  // Define handleClerkAuth with useCallback
  const handleClerkAuth = useCallback(async () => {
    // Check if we already have a valid token
    const existingToken =
      localStorage.getItem("wedzat_token") ||
      localStorage.getItem("jwt_token") ||
      localStorage.getItem("token");

    if (existingToken) {
      console.log("Valid token exists, skipping auth call");
      router.push(
        currentView === "vendorSignup" ? "/vendor/dashboard" : "/home"
      );
      return;
    }

    if (!isAuthLoaded || !isSignedIn || !user) {
      console.error("Auth not loaded or user not signed in");
      return null;
    }

    console.log("Starting Clerk authentication process");

    try {
      // Get the token from Clerk
      let token: string;

      // Try to get token without specifying a template first
      try {
        token = await window.Clerk.session.getToken();
        console.log("Got token without template");
      } catch (tokenError) {
        console.error("Error getting token without template:", tokenError);

        // If that fails, try with the default template
        try {
          token = await window.Clerk.session.getToken({ template: "default" });
          console.log("Got token with default template");
        } catch (defaultError) {
          console.error(
            "Error getting token with default template:",
            defaultError
          );
          throw new Error("Could not retrieve authentication token");
        }
      }

      if (!token) {
        throw new Error("No token received from Clerk");
      }

      console.log("Token retrieved successfully: Token exists");

      // Extract user info to send along with the token
      const userEmail = user?.primaryEmailAddress?.emailAddress || "";
      const userName = user?.fullName || "";
      const userId = user?.id || "";

      console.log("Sending user info to backend:", {
        email: userEmail,
        name: userName,
        id: userId,
      });

      // Call your backend API to authenticate with Clerk token
      console.log("Attempting to connect to backend...");
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000";
      console.log(`Using API URL: ${apiUrl}`);
      const response = await axios.post(
        `${apiUrl}/auth_clerk`,
        {
          clerk_token: token,
          user_type: currentView === "vendorSignup" ? "vendor" : "normal",
          user_email: userEmail,
          user_name: userName,
          user_id: userId,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      console.log("Backend response:", response);

      // Handle response from backend
      console.log("Backend response data:", response.data);

      // Check if response contains a token directly
      if (response.data && response.data.token) {
        console.log("Found token in direct response");
        // Store the backend JWT token
        localStorage.setItem("wedzat_token", response.data.token);

        // Close popup
        closePopup();

        // Redirect based on user type
        console.log(
          `Redirecting to ${
            currentView === "vendorSignup" ? "/vendor/dashboard" : "/home"
          }`
        );

        // Use router for navigation instead of window.location
        if (currentView === "vendorSignup") {
          router.push("/vendor/dashboard");
        } else {
          router.push("/home");
        }

        // Force navigation if router doesn't work
        setTimeout(() => {
          if (currentView === "vendorSignup") {
            window.location.href = "/vendor/dashboard";
          } else {
            window.location.href = "/home";
          }
        }, 500);
        return; // Exit early after successful authentication
      }

      // Check for nested response structure (old format)
      if (
        response.data &&
        typeof response.data === "object" &&
        response.data.body
      ) {
        try {
          // Try to parse the body if it's a string
          const parsedBody =
            typeof response.data.body === "string"
              ? JSON.parse(response.data.body)
              : response.data.body;

          console.log("Parsed body:", parsedBody);

          // Check if the nested response contains an error
          if (
            response.data.statusCode >= 400 ||
            (parsedBody && parsedBody.error)
          ) {
            throw new Error(
              (parsedBody && parsedBody.error) ||
                "Backend authentication failed"
            );
          }

          // Check for token in the parsed body
          if (parsedBody && parsedBody.token) {
            // Store the backend JWT token
            localStorage.setItem("wedzat_token", parsedBody.token);

            // Close popup
            closePopup();

            // Redirect based on user type
            console.log(
              `Redirecting to ${
                currentView === "vendorSignup" ? "/vendor/dashboard" : "/home"
              }`
            );

            // Use router for navigation instead of window.location
            if (currentView === "vendorSignup") {
              router.push("/vendor/dashboard");
            } else {
              router.push("/home");
            }

            // Force navigation if router doesn't work
            setTimeout(() => {
              if (currentView === "vendorSignup") {
                window.location.href = "/vendor/dashboard";
              } else {
                window.location.href = "/home";
              }
            }, 500);
            return; // Exit early after successful authentication
          }
        } catch (parseError) {
          console.error("Error parsing response body:", parseError);
          throw new Error("Invalid response format from backend");
        }
      }

      // Check if there's a database error in the response
      if (
        response.data &&
        response.data.body &&
        typeof response.data.body === "string" &&
        (response.data.body.includes("violates not-null constraint") ||
          response.data.body.includes("violates check constraint"))
      ) {
        console.error(
          "Database constraint violation error. Using fallback approach..."
        );

        // Instead of retrying, let's use the Clerk token directly as a fallback
        console.log(
          "Using Clerk token as fallback due to database constraints"
        );
        localStorage.setItem("wedzat_token", token);
        closePopup();

        // Redirect based on user type
        console.log(
          `Redirecting to ${
            currentView === "vendorSignup" ? "/vendor/dashboard" : "/home"
          }`
        );

        // Use router for navigation instead of window.location
        if (currentView === "vendorSignup") {
          router.push("/vendor/dashboard");
        } else {
          router.push("/home");
        }

        // Force navigation if router doesn't work
        setTimeout(() => {
          if (currentView === "vendorSignup") {
            window.location.href = "/vendor/dashboard";
          } else {
            window.location.href = "/home";
          }
        }, 500);
        return;
      }

      // If we get here, we didn't find a token in the response
      console.log("No token found in response, using fallback");

      // FALLBACK: Use the Clerk token directly if backend response is invalid
      console.log("Using Clerk token as fallback");
      localStorage.setItem("wedzat_token", token);
      closePopup();

      // Redirect based on user type
      console.log(
        `Redirecting to ${
          currentView === "vendorSignup" ? "/vendor/dashboard" : "/home"
        }`
      );

      // Use router for navigation instead of window.location
      if (currentView === "vendorSignup") {
        router.push("/vendor/dashboard");
      } else {
        router.push("/home");
      }

      // Force navigation if router doesn't work
      setTimeout(() => {
        if (currentView === "vendorSignup") {
          window.location.href = "/vendor/dashboard";
        } else {
          window.location.href = "/home";
        }
      }, 500);
      return;
    } catch (error) {
      console.error("Backend error:", error);
      return null;
    }
  }, [isAuthLoaded, isSignedIn, user, closePopup, currentView, router]);

  // Auth state conditional rendering
  useEffect(() => {
    if (isAuthLoaded && isSignedIn && user) {
      // User is signed in, close popup and/or redirect
      closePopup();
      // Try to authenticate with backend
      handleClerkAuth();
    }
  }, [isAuthLoaded, isSignedIn, user, closePopup, handleClerkAuth]);

  // Shared handlers
  const handleLoginClick = () => {
    console.log("Login clicked");
    // Navigate to the user login view
    setCurrentView("userLogin");
  };

  const handleUserLogin = (credentials: Record<string, unknown>) => {
    console.log("User login:", credentials);
    // Implement user login logic - already handled in the UserLogin component
    closePopup();
  };

  const handleVendorSignupClick = () => {
    console.log("Vendor signup clicked");
    navigateToVendorSignup();
  };

  const handleRegistrationSubmit = (data: Record<string, unknown>) => {
    console.log("Registration submitted:", data);
    // Implement registration submission
    setLoginPopUp(false);
    // Show success message or redirect
  };

  useEffect(() => {
    const checkAuthStatus = async () => {
      // Check for token in localStorage
      const token = localStorage.getItem("token");

      if (token) {
        try {
          // Optional: Verify token is valid with a backend call
          // const isValid = await authService.verifyToken(token);
          // if (isValid) {
          //   router.replace('/home');
          // }
          // For now, just redirect if token exists
          router.replace("/home");
        } catch (error) {
          console.error("Error verifying auth token:", error);
          // If token verification fails, clear it
          localStorage.removeItem("token");
        }
      }
    };

    checkAuthStatus();
  }, [router]);

  // handleClerkAuth is now defined above with useCallback

  // Determine if we're on a verification or registration screen
  const isVerificationOrRegistration =
    currentView === "otp" ||
    currentView === "emailVerification" ||
    currentView === "registration";

  // Determine if we're on a vendor screen
  const isVendorScreen =
    currentView === "vendorLogin" || currentView === "vendorSignup";

  return (
    <div
      className={`flex flex-col md:flex-row items-center justify-center min-h-screen ${
        loginPopUp
          ? "bg-black text-white transition-colors duration-300"
          : "bg-white text-black"
      } p-4`}
    >
      <div className="flex flex-col items-center text-center mr-16 -ml-8">
        <div className="mb-0 flex justify-center">
          {/* Animated logo with color changing effect */}
          <div className="mr-10 relative w-[130px] h-[100px]">
            {!sequenceComplete ? (
              // Show frame sequence until complete
              logoFrames.map((frame, index) => (
                <div
                  key={index}
                  className="absolute top-0 left-0 transition-opacity duration-500"
                  style={{
                    opacity: currentFrame === index ? 1 : 0,
                    zIndex: index,
                  }}
                >
                  <Image
                    src={frame}
                    alt={`WEDZAT Logo Frame ${index + 1}`}
                    width={130}
                    height={100}
                    priority
                    className="object-contain"
                  />
                </div>
              ))
            ) : (
              // After sequence complete, show final frame with color transitions
              <div
                className="transition-all duration-500 ease-in-out"
                style={logoStyle}
              >
                <Image
                  src={logoFrames[logoFrames.length - 1]} // Final frame
                  alt="WEDZAT Logo"
                  width={130}
                  height={100}
                  priority
                  className="object-contain"
                />
              </div>
            )}
          </div>
        </div>
        <div>
          <Image
            src="/pics/unnamed.png"
            alt="WEDZAT Text"
            width={200}
            height={200}
            priority
            className="object-contain mr-10 mb-8 mt-2 "
          />
        </div>

        <div className="flex flex-col items-center text-center">
          {/* "Your wedding" Text */}
          <h1 className="text-6xl md:text-7xl leading-tight tracking-[-0.03em] text-center font-bold">
            {/* "Your" - Plus Jakarta Sans */}
            <span className="font-jakarta font-medium bg-gradient-to-r from-[#CCAB75] to-[#CCAB75] bg-clip-text text-transparent">
              Your{" "}
            </span>

            {/* "wedding" - Instrument Serif with Gradient */}
            <span className="font-serif font-normal italic bg-gradient-to-r from-[#CCAB75] to-[#000000] bg-clip-text text-transparent">
              wedding
            </span>
          </h1>

          {/* Image Circles & "Platform" Text */}
          <div className="flex items-center justify-center mt-1 mb-6">
            {/* Image Circles with Shadow Borders */}
            <div className="flex -space-x-4 mr-6">
              {/* First Circle */}
              <div
                className="w-12 h-12 md:w-20 md:h-20 relative shadow-lg rounded-full"
                style={{ boxShadow: "0 0 10px rgba(0, 0, 0, 0.3)" }}
              >
                <Image
                  src="/pics/1stim.jfif"
                  alt="Wedding photo"
                  fill
                  className="object-cover rounded-full border-2 border-white"
                />
              </div>

              {/* Second Circle */}
              <div
                className="w-12 h-12 md:w-20 md:h-20 relative shadow-lg rounded-full"
                style={{ boxShadow: "0 0 10px rgba(0, 0, 0, 0.3)" }}
              >
                <Image
                  src="/pics/2ndim.jfif"
                  alt="Wedding photo"
                  fill
                  className="object-cover rounded-full border-2 border-white"
                />
              </div>

              {/* Third Circle */}
              <div
                className="w-12 h-12 md:w-20 md:h-20 relative shadow-lg rounded-full"
                style={{ boxShadow: "0 0 10px rgba(0, 0, 0, 0.3)" }}
              >
                <Image
                  src="/pics/jpeg3.jfif"
                  alt="Wedding photo"
                  fill
                  className="object-cover rounded-full border-2 border-white"
                />
              </div>
            </div>

            {/* "Platform" Text */}
            <h1 className="text-6xl md:text-7xl leading-tight tracking-[-0.03em] font-jakarta font-medium text-center">
              <span className="bg-gradient-to-r from-[#CCAB76] to-[#66563B] bg-clip-text text-transparent">
                platform
              </span>
            </h1>
          </div>
        </div>
      </div>

      {/* Added more space between sections */}
      <div className="w-full max-w-md gap-3 md:ml-12">
        <div className="text-center md:text-left mb-12">
          <h1 className="text-4xl font-bold mb-3">
            Welcome to <span className="text-red-700">Wedzat</span>
          </h1>
          <p className="text-lg text-gray-700 font-medium">
            Choose your Account
          </p>
        </div>

        <div className="space-y-6">
          {/* User Login Button */}
          <button
            onClick={() => {
              setLoginPopUp(true);
              setCurrentView("userLogin"); // Open user login directly
            }}
            className={`w-full flex items-center px-6 py-4 rounded-full transition duration-300 border ${
              loginPopUp
                ? "bg-black text-white border-white"
                : "bg-white text-black border-gray-300 hover:bg-gray-50"
            }`}
          >
            <User size={22} className="mr-4 text-gray-700" />
            <span className="font-medium flex-grow text-center text-lg">
              User Login
            </span>
          </button>

          <div className="relative flex items-center justify-center my-8">
            <div className="flex-grow border-t border-gray-300"></div>
            <span className="flex-shrink mx-4 text-lg text-gray-500 font-medium">
              OR
            </span>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>

          {/* Business Login Button */}
          <button
            onClick={() => {
              setLoginPopUp(true);
              setCurrentView("vendorLogin");
            }}
            className={`w-full flex items-center px-6 py-4 rounded-full transition duration-300 border ${
              loginPopUp
                ? "bg-black text-white border-white"
                : "bg-white text-black border-gray-300 hover:bg-gray-50"
            }`}
          >
            <Building size={22} className="mr-4 text-gray-700" />
            <span className="font-medium flex-grow text-center text-lg">
              Business Login
            </span>
          </button>
        </div>
      </div>

      {loginPopUp && (
        <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
          <div
            className="relative rounded-lg max-w-md w-full pt-2 mx-auto overflow-hidden text-gray-900"
            style={{
              background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
              maxHeight: "85vh",
              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            }}
          >
            <button
              onClick={closePopup}
              className="absolute top-4 right-4 flex items-center justify-center w-8 h-8 text-[#646B7D] hover:text-[#B62E2E] focus:outline-none focus:ring-2 focus:ring-[#B62E2E] focus:ring-opacity-50 rounded-full transition-all duration-200 transform hover:scale-110 active:scale-95 z-10 bg-transparent hover:bg-[#f8f1e4] group"
              aria-label="Close registration form"
              type="button"
              tabIndex={0}
            >
              <span className="sr-only">close</span>
              <X
                size={24}
                strokeWidth={2.5}
                className="transition-transform duration-200 group-hover:rotate-90"
              />
            </button>

            {/* Authentication method tabs - only show for original login/email login views */}
            {!isVerificationOrRegistration &&
              !isVendorScreen &&
              currentView !== "userLogin" && (
                <div className="flex justify-center mb-6 border-b border-gray-200">
                  <button
                    onClick={() => switchAuthMethod("mobile")}
                    className={`flex items-center px-6 py-3 ${
                      authMethod === "mobile"
                        ? "text-red-700 border-b-2 border-red-700 font-medium"
                        : "text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    <Phone size={18} className="mr-2" />
                    <span>Mobile</span>
                  </button>
                  <button
                    onClick={() => switchAuthMethod("email")}
                    className={`flex items-center px-6 py-3 ${
                      authMethod === "email"
                        ? "text-red-700 border-b-2 border-red-700 font-medium"
                        : "text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    <Mail size={18} className="mr-2" />
                    <span>Email</span>
                  </button>
                </div>
              )}

            {/* Content container with hidden scrollbar */}
            <div
              className="scale-95 origin-top overflow-y-auto scrollbar-hide px-4 py-2"
              style={{
                maxHeight: "calc(85vh - 60px)",
                scrollbarWidth: "none" /* Firefox */,
                msOverflowStyle: "none" /* IE and Edge */,
              }}
            >
              {/* Add a CSS style to hide the scrollbar for webkit browsers */}
              <style jsx>{`
                .scrollbar-hide::-webkit-scrollbar {
                  display: none;
                }

                /* Add custom font rendering for better clarity */
                body {
                  text-rendering: optimizeLegibility;
                  -webkit-font-smoothing: antialiased;
                  -moz-osx-font-smoothing: grayscale;
                }

                /* Increase font sizes in all form elements but keep professional */
                input,
                select,
                textarea,
                button {
                  font-size: 1rem !important;
                  letter-spacing: 0.01em !important;
                }

                /* Improve readability */
                label {
                  font-weight: 500 !important;
                  margin-bottom: 0.25rem !important;
                }

                /* Add smooth transitions for better UI experience */
                button,
                a {
                  transition: all 0.3s ease;
                }
              `}</style>

              {/* USER AUTHENTICATION VIEWS */}

              {/* User Login View - NEW */}
              {currentView === "userLogin" && (
                <UserLogin
                  onBack={closePopup}
                  onLogin={handleUserLogin}
                  onForgotPassword={handleForgotPassword}
                  onSignupClick={() => setCurrentView("login")}
                  onSocialSignin={handleSocialSignin}
                />
              )}

              {/* Mobile Login View - ORIGINAL */}
              {currentView === "login" &&
                (isSignedIn ? (
                  <div className="text-center mt-4">
                    <p className="mb-4">
                      You are logged in as{" "}
                      {user?.fullName || user?.emailAddresses[0]?.emailAddress}
                    </p>
                    <SignOutButton>
                      <button className="w-full bg-red-700 text-white py-3 rounded-md mb-4 hover:bg-red-800 transition duration-200">
                        Sign Out
                      </button>
                    </SignOutButton>
                  </div>
                ) : (
                  <Login
                    onOtpRequest={handleOtpRequest}
                    onSocialLogin={handleSocialLogin}
                    onLogin={handleLoginClick}
                    onVendorSignup={handleVendorSignupClick}
                  />
                ))}

              {/* Email Login View */}
              {currentView === "emailLogin" && !isSignedIn && (
                <EmailLogin
                  onEmailVerify={handleEmailVerify}
                  onSocialLogin={handleSocialLogin}
                  onLogin={handleLoginClick}
                  onVendorSignup={handleVendorSignupClick}
                />
              )}

              {/* Mobile OTP Verification View */}
              {currentView === "otp" && (
                <OtpVerification
                  mobileNumber={mobileNumber}
                  onVerify={handleOtpVerify}
                  onClose={() => setCurrentView("login")}
                  onResendOtp={handleResendOtp}
                  onLoginClick={handleLoginClick}
                  onVendorSignupClick={handleVendorSignupClick}
                />
              )}

              {/* Email Verification View */}
              {currentView === "emailVerification" && (
                <EmailVerification
                  email={email}
                  onVerify={handleEmailCodeVerify}
                  onClose={() => setCurrentView("emailLogin")}
                  onResendCode={handleResendEmailCode}
                  onLoginClick={handleLoginClick}
                  onVendorSignupClick={handleVendorSignupClick}
                />
              )}

              {/* Registration View */}
              {currentView === "registration" && (
                <Registration
                  onComplete={handleRegistrationSubmit}
                  onBack={() => setCurrentView(email ? "emailLogin" : "login")}
                />
              )}

              {/* VENDOR AUTHENTICATION VIEWS */}

              {/* Vendor Login View */}
              {currentView === "vendorLogin" && (
                <VendorLogin
                  onLogin={handleVendorLogin}
                  onForgotPassword={handleForgotPassword}
                  onSignupClick={navigateToVendorSignup}
                  onBack={closePopup}
                />
              )}

              {/* Vendor Signup View */}
              {currentView === "vendorSignup" && (
                <VendorSignup
                  onSignup={handleVendorSignup}
                  onLoginClick={navigateToVendorLogin}
                  onBack={closePopup}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WedzatLoginPage;
