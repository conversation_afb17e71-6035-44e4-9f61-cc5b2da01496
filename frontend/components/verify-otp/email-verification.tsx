// components/verify-otp/email-verification.tsx
import React, { useState, ChangeEvent, FormEvent } from "react";
import Image from "next/image";
import { ArrowLeft } from "lucide-react";

interface EmailVerificationProps {
  email: string;
  onVerify: (code: string) => void;
  onClose: () => void;
  onResendCode: () => void;
  onLoginClick?: () => void;
  onVendorSignupClick?: () => void;
}

const EmailVerification: React.FC<EmailVerificationProps> = ({
  email,
  onVerify,
  onClose,
  onResendCode,
  onLoginClick,
  onVendorSignupClick,
}) => {
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [showPasswordFields, setShowPasswordFields] = useState<boolean>(false);
  const [passwordError, setPasswordError] = useState<string>("");

  const handleCodeChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value.replace(/[^0-9]/g, "");
    setVerificationCode(value);
    
    // Clear error when typing
    if (error) setError("");
  };

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setPassword(e.target.value);
    if (passwordError) validatePasswords();
  };

  const handleConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setConfirmPassword(e.target.value);
    if (passwordError) validatePasswords();
  };

  const validatePasswords = (): boolean => {
    if (password.length < 8) {
      setPasswordError("Password must be at least 8 characters long");
      return false;
    }
    
    if (password !== confirmPassword) {
      setPasswordError("Passwords do not match");
      return false;
    }
    
    setPasswordError("");
    return true;
  };

  const handleVerify = async (e: FormEvent): Promise<void> => {
    e.preventDefault();
    
    if (!verificationCode) {
      setError("Please enter the verification code");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      // Call the API endpoint to verify OTP
      const response = await fetch('/api/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          otp: verificationCode 
        }),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        // Email is verified, store it and show password fields
        setShowPasswordFields(true);
        // Also store in sessionStorage
        sessionStorage.setItem('verifiedEmail', email);
      } else {
        setError(data.message || "Invalid verification code. Please try again.");
      }
    } catch (error) {
      console.error("Error verifying code:", error);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinue = (e: FormEvent): void => {
    e.preventDefault();
    
    if (!validatePasswords()) {
      return;
    }
    
    // Store password in sessionStorage to pass to registration
    sessionStorage.setItem('userPassword', password);
    
    // Call onVerify callback to proceed to registration
    onVerify(verificationCode);
  };

  const handleResend = (): void => {
    onResendCode();
  };

  const handleLoginClick = (e: React.MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    if (onLoginClick) {
      onLoginClick();
    }
  };

  const handleVendorSignupClick = (e: React.MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    if (onVendorSignupClick) {
      onVendorSignupClick();
    }
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full px-6 py-6 rounded-lg">
        {/* Back Button */}
        <button
          onClick={onClose}
          className="absolute top-4 left-4 text-gray-600 hover:text-gray-800 flex items-center"
        >
          <ArrowLeft size={18} />
        </button>
        
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        {/* Heading */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">
            Email Verification
          </h1>
          <p className="text-sm mt-1">
            {showPasswordFields 
              ? "Create your password" 
              : `We've sent a verification code to ${email}`}
          </p>
        </div>

        {/* Form */}
        <form onSubmit={showPasswordFields ? handleContinue : handleVerify} className="space-y-4">
          {!showPasswordFields ? (
            // Verification code input
            <>
              <div className="mb-4">
                <input
                  type="text"
                  placeholder="Enter verification code"
                  maxLength={6}
                  className={`w-full p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={verificationCode}
                  onChange={handleCodeChange}
                  disabled={isLoading}
                />
                
                {error && (
                  <p className="text-red-500 text-sm mt-1">{error}</p>
                )}
                
                <button
                  type="button"
                  onClick={handleResend}
                  className="text-red-700 text-sm mt-2 hover:underline"
                  disabled={isLoading}
                >
                  Resend Code
                </button>
              </div>
              
              <button
                type="submit"
                className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 disabled:bg-red-300"
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Verify"}
              </button>
            </>
          ) : (
            // Password creation after verification
            <>
              <div className="mb-4">
                <div className="mb-1">
                  <label htmlFor="password" className="text-sm font-medium text-gray-700">
                    Create Password
                  </label>
                </div>
                <input
                  id="password"
                  type="password"
                  placeholder="Enter new password"
                  className={`w-full p-3 border ${passwordError ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={password}
                  onChange={handlePasswordChange}
                  disabled={isLoading}
                />
              </div>

              <div className="mb-4">
                <div className="mb-1">
                  <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                    Confirm Password
                  </label>
                </div>
                <input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  className={`w-full p-3 border ${passwordError ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  disabled={isLoading}
                />
                
                {passwordError && (
                  <p className="text-red-500 text-sm mt-1">{passwordError}</p>
                )}
              </div>
              
              <button
                type="submit"
                className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 disabled:bg-red-300"
                disabled={isLoading}
              >
                Continue
              </button>
            </>
          )}
        </form>

        {/* Footer Links */}
        <div className="text-center p-2 mt-4">
          <div className="rounded-lg mb-2">
            <span className="text-gray-700 text-sm">Already A member - </span>
            <a
              href="#"
              className="text-[#6A39A4] text-sm font-bold"
              onClick={handleLoginClick}
            >
              Log in
            </a>
          </div>
          <div className="rounded-lg">
            <span className="text-gray-700 text-sm">Are you a Vendor - </span>
            <a
              href="#"
              className="text-blue-600 text-sm font-bold"
              onClick={handleVendorSignupClick}
            >
              Create Account
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerification;