import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { <PERSON>Left, Eye, EyeOff } from "lucide-react";

interface OtpVerificationProps {
    mobileNumber: string;
    onVerify: (otp: string) => void;
    onClose: () => void;
    onResendOtp: () => void;
    onLoginClick: () => void;
    onVendorSignupClick: () => void;
}

const OtpVerification: React.FC<OtpVerificationProps> = ({
    mobileNumber,
    onVerify,
    onClose,
    onResendOtp,
    onLoginClick,
    onVendorSignupClick,
}) => {
    // OTP state
    const [otp, setOtp] = useState<string[]>(["", "", "", "", "", ""]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string>("");
    const [resendDisabled, setResendDisabled] = useState<boolean>(false);
    const [resendCountdown, setResendCountdown] = useState<number>(0);
    const [debugInfo, setDebugInfo] = useState<string>("");
    
    // Password state
    const [password, setPassword] = useState<string>("");
    const [confirmPassword, setConfirmPassword] = useState<string>("");
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [passwordError, setPasswordError] = useState<string>("");
    
    // Step state
    const [currentStep, setCurrentStep] = useState<'verifyOtp' | 'createPassword'>('verifyOtp');
    
    const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

    // Initialize refs array - now for 6 digits
    useEffect(() => {
        inputRefs.current = inputRefs.current.slice(0, 6);
        // Focus first input when component mounts
        setTimeout(() => {
            inputRefs.current[0]?.focus();
        }, 100);
    }, []);

    // Handle resend countdown
    useEffect(() => {
        if (resendCountdown <= 0) return;
        
        const timer = setTimeout(() => {
            setResendCountdown(prev => prev - 1);
        }, 1000);
        
        return () => clearTimeout(timer);
    }, [resendCountdown]);

    const handleChange = (index: number, value: string) => {
        // Only allow numbers
        if (!/^\d*$/.test(value)) return;
        
        // Clear error when typing
        if (error) setError("");

        const newOtp = [...otp];
        newOtp[index] = value.substring(0, 1); // Only take the first character
        setOtp(newOtp);

        // Auto-focus next input if value is entered
        if (value && index < 5) {
            inputRefs.current[index + 1]?.focus();
        }

        // Check if all inputs are filled
        if (value && index === 5) {
            const isComplete = newOtp.every(digit => digit.length === 1);
            if (isComplete) {
                // Optional: Auto-verify after a slight delay
                // setTimeout(() => {
                //    handleVerify();
                // }, 300);
            }
        }
    };

    // Fill OTP fields with provided value (for development)
    const fillOtpFields = (otpValue: string) => {
        if (!otpValue || otpValue.length !== 6) return;
        
        const otpArray = otpValue.split('');
        setOtp(otpArray);
        setDebugInfo(`Auto-filled OTP: ${otpValue}`);
    };

    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        // Move to previous input on backspace if current input is empty
        if (e.key === "Backspace" && !otp[index] && index > 0) {
            inputRefs.current[index - 1]?.focus();
        }
    };

    const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setPassword(e.target.value);
        if (passwordError) validatePasswords();
    };

    const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setConfirmPassword(e.target.value);
        if (passwordError) validatePasswords();
    };

    const validatePasswords = (): boolean => {
        if (password.length < 8) {
            setPasswordError("Password must be at least 8 characters long");
            return false;
        }
        
        if (password !== confirmPassword) {
            setPasswordError("Passwords do not match");
            return false;
        }
        
        setPasswordError("");
        return true;
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const toggleConfirmPasswordVisibility = () => {
        setShowConfirmPassword(!showConfirmPassword);
    };

    const handleVerify = async () => {
        const otpValue = otp.join("");
        if (otpValue.length !== 6) {
            setError("Please enter the complete 6-digit OTP");
            return;
        }
        
        setIsLoading(true);
        setError("");
        setDebugInfo("Verifying OTP...");
        
        try {
            // Call API to verify OTP
            const response = await fetch('/api/verify-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    otp: otpValue,
                    phone: mobileNumber 
                }),
            });
            
            const data = await response.json();
            
            // For debugging
            if (process.env.NODE_ENV === 'development') {
                setDebugInfo(prev => prev + `\nResponse: ${JSON.stringify(data)}, Status: ${response.status}`);
            }
            
            if (response.ok && data.success) {
                // Store verified mobile in session storage
                sessionStorage.setItem('verifiedMobile', mobileNumber);
                
                // Move to password creation step
                setCurrentStep('createPassword');
                setDebugInfo(prev => prev + "\nOTP verified successfully. Please create your password.");
            } else {
                setError(data.message || "Invalid OTP. Please try again.");
            }
        } catch (error) {
            console.error("Error verifying OTP:", error);
            setError("An unexpected error occurred. Please try again.");
            if (process.env.NODE_ENV === 'development' && error instanceof Error) {
                setDebugInfo(prev => prev + `\nError: ${error.message}`);
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleCreatePassword = async () => {
        if (!validatePasswords()) {
            return;
        }
        
        setIsLoading(true);
        setDebugInfo(prev => prev + "\nCreating password...");
        
        try {
            // Store password in session storage
            sessionStorage.setItem('userPassword', password);
            
            setDebugInfo(prev => prev + "\nPassword stored successfully. Proceeding to registration...");
            
            // Add a small delay to show the success message
            setTimeout(() => {
                // Call the onVerify callback to proceed to registration
                onVerify(otp.join(""));
            }, 500);
        } catch (error) {
            console.error("Error storing password:", error);
            setPasswordError("An unexpected error occurred. Please try again.");
            if (process.env.NODE_ENV === 'development' && error instanceof Error) {
                setDebugInfo(prev => prev + `\nError: ${error.message}`);
            }
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleResendOtp = async () => {
        if (resendDisabled) return;
        
        setResendDisabled(true);
        setResendCountdown(30); // 30 seconds cooldown
        
        try {
            // Call API to resend OTP
            const response = await fetch('/api/resend-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ phone: mobileNumber }), // Add phone to request
            });
            
            const data = await response.json();
            
            if (!response.ok || !data.success) {
                // Show error but keep countdown running
                setError(data.message || "Failed to resend OTP. Please try again later.");
            } else {
                // Clear any previous errors
                setError("");
                // Call the onResendOtp prop
                onResendOtp();
                
                // For development mode - auto-fill OTP if provided
                if (process.env.NODE_ENV === 'development' && data.devOtp) {
                    setDebugInfo(`Dev OTP: ${data.devOtp}`);
                    fillOtpFields(data.devOtp);
                }
            }
        } catch (error) {
            console.error("Error resending OTP:", error);
            setError("Failed to resend OTP. Please try again later.");
        }
        
        // Timer will automatically enable the resend button after countdown
        setTimeout(() => {
            setResendDisabled(false);
        }, 30000);
    };

    const formattedNumber = mobileNumber.startsWith("+")
        ? mobileNumber
        : `+91 ${mobileNumber}`;

    return (
        <div className="p-4 w-full">
            {/* Back Button */}
            <button
                onClick={onClose}
                className="text-gray-600 hover:text-gray-800 flex items-center mb-6"
                disabled={isLoading}
            >
                <ArrowLeft size={18} />
            </button>

            {/* Logo */}
            <div className="flex justify-center mb-6">
                <div className="text-red-600">
                    <Image
                        src="/pics/logo.png"
                        alt="Wedzat logo"
                        width={40}
                        height={40}
                        className="object-cover"
                    />
                </div>
            </div>

            {/* Header */}
            <div className="text-center mb-6">
                <h1 className="text-2xl font-bold">
                    {currentStep === 'verifyOtp' ? 'OTP Verification' : 'Create Password'}
                </h1>
                <p className="text-sm text-gray-600 mt-2">
                    {currentStep === 'verifyOtp' 
                        ? `Enter the 6-digit OTP sent to ${formattedNumber}`
                        : 'Create a secure password for your account'}
                </p>
            </div>

            {/* Error Message */}
            {error && (
                <div className="text-center mb-4">
                    <p className="text-red-500 text-sm">{error}</p>
                </div>
            )}
            
            {/* Password Error Message */}
            {passwordError && currentStep === 'createPassword' && (
                <div className="text-center mb-4">
                    <p className="text-red-500 text-sm">{passwordError}</p>
                </div>
            )}
            
            {/* OTP Verification Step */}
            {currentStep === 'verifyOtp' && (
                <>
                    {/* OTP Input Fields */}
                    <div className="flex justify-between mb-6 gap-1">
                        {[0, 1, 2, 3, 4, 5].map((index) => (
                            <div key={index} className="w-1/6">
                                <input
                                    ref={(el) => { inputRefs.current[index] = el; }}
                                    type="text"
                                    maxLength={1}
                                    className={`w-full h-12 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white text-center text-xl font-bold`}
                                    value={otp[index]}
                                    onChange={(e) => handleChange(index, e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(index, e)}
                                    disabled={isLoading}
                                />
                            </div>
                        ))}
                    </div>
                    
                    {/* Resend OTP Link */}
                    <div className="text-center mb-6">
                        <span className="text-gray-600 text-sm">If you haven't received the OTP? </span>
                        <button
                            className={`text-red-500 text-sm font-medium ${resendDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={handleResendOtp}
                            disabled={resendDisabled || isLoading}
                        >
                            {resendCountdown > 0 ? `RESEND OTP (${resendCountdown}s)` : 'RESEND OTP'}
                        </button>
                    </div>

                    {/* Verify Button */}
                    <button
                        className="w-full bg-red-700 text-white py-3 rounded-md mb-6 hover:bg-red-800 transition duration-200 disabled:bg-red-300"
                        onClick={handleVerify}
                        disabled={isLoading}
                    >
                        {isLoading ? "Verifying..." : "Verify & Continue"}
                    </button>
                </>
            )}

            {/* Create Password Step */}
            {currentStep === 'createPassword' && (
                <>
                    {/* Password Fields */}
                    <div className="space-y-4 mb-6">
                        {/* Password Field */}
                        <div className="relative">
                            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                Create Password
                            </label>
                            <div className="relative">
                                <input
                                    id="password"
                                    type={showPassword ? "text" : "password"}
                                    placeholder="Enter new password"
                                    className={`w-full p-3 border ${passwordError ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                                    value={password}
                                    onChange={handlePasswordChange}
                                    disabled={isLoading}
                                    minLength={8}
                                />
                                <button
                                    type="button"
                                    onClick={togglePasswordVisibility}
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                >
                                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                </button>
                            </div>
                        </div>
                        
                        {/* Confirm Password Field */}
                        <div className="relative">
                            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                Confirm Password
                            </label>
                            <div className="relative">
                                <input
                                    id="confirmPassword"
                                    type={showConfirmPassword ? "text" : "password"}
                                    placeholder="Confirm your password"
                                    className={`w-full p-3 border ${passwordError ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                                    value={confirmPassword}
                                    onChange={handleConfirmPasswordChange}
                                    disabled={isLoading}
                                    minLength={8}
                                />
                                <button
                                    type="button"
                                    onClick={toggleConfirmPasswordVisibility}
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                >
                                    {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                </button>
                            </div>
                        </div>
                        
                        <p className="text-xs text-gray-500">
                            Password must be at least 8 characters long
                        </p>
                    </div>

                    {/* Continue Button */}
                    <button
                        className="w-full bg-red-700 text-white py-3 rounded-md mb-6 hover:bg-red-800 transition duration-200 disabled:bg-red-300"
                        onClick={handleCreatePassword}
                        disabled={isLoading}
                    >
                        {isLoading ? "Processing..." : "Continue"}
                    </button>
                </>
            )}

            {/* Footer Links */}
            <div className="text-center">
                <div className="mb-2">
                    <span className="text-gray-600 text-sm">Already A member - </span>
                    <button
                        className="text-[#6A39A4] text-sm font-bold"
                        onClick={onLoginClick}
                        disabled={isLoading}
                    >
                        Log in
                    </button>
                </div>
                <div>
                    <span className="text-gray-600 text-sm">Are you a Vendor - </span>
                    <button
                        className="text-blue-600 text-sm font-bold"
                        onClick={onVendorSignupClick}
                        disabled={isLoading}
                    >
                        Create Account
                    </button>
                </div>
            </div>
        </div>
    );
};

export default OtpVerification;