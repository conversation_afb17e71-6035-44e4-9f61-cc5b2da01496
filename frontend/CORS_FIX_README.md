# CORS Fix for S3 Uploads

This document explains the implementation of CORS error handling for S3 uploads in the WedZat application.

## Problem

When uploading files directly to S3 from the browser, CORS (Cross-Origin Resource Sharing) errors can occur if the S3 bucket is not properly configured to accept requests from your domain. This results in errors like:

```
20250520120925.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA25...&partNumber=1&uploadId=direct-upload CORS error xhr
```

## Solution

The solution implements a fallback mechanism that automatically switches to a proxy upload when CORS errors are detected:

1. **Try Direct Upload First**: The application attempts to upload directly to S3 for maximum performance
2. **Detect CORS Errors**: If a CORS error occurs, it's detected based on the error message
3. **Fallback to Proxy**: When a CORS error is detected, the upload automatically falls back to using the server-side proxy
4. **Seamless Experience**: This happens transparently to the user, with no interruption in the upload process

## Implementation Details

### 1. Multipart Upload (for Large Files)

For large files that use multipart upload:

- Each chunk is first attempted to be uploaded directly to S3
- If a CORS error occurs, that chunk is automatically uploaded through the proxy API
- This approach provides the best performance while ensuring reliability

### 2. Direct Upload (for Small Files)

For small files that use direct upload:

- The file is first attempted to be uploaded directly to S3
- If a CORS error occurs, the entire file is uploaded through the proxy API
- This ensures small files are uploaded efficiently while handling CORS issues

### 3. CORS Error Detection

CORS errors are detected by checking for specific patterns in error messages:
- "CORS"
- "Network Error"
- "Failed to fetch"
- "cross-origin"

## Optimal S3 Bucket Configuration

To avoid CORS errors entirely and achieve the best performance, configure your S3 bucket with the following CORS configuration:

```json
[
  {
    "AllowedHeaders": [
      "*"
    ],
    "AllowedMethods": [
      "PUT",
      "POST",
      "GET",
      "HEAD"
    ],
    "AllowedOrigins": [
      "https://wedzat.com",
      "https://www.wedzat.com",
      "http://localhost:3000"
    ],
    "ExposeHeaders": [
      "ETag",
      "x-amz-request-id"
    ],
    "MaxAgeSeconds": 3600
  }
]
```

## Performance Considerations

1. **Direct Upload**: Fastest method when CORS is properly configured
2. **Proxy Upload**: Slightly slower but more reliable when CORS issues exist
3. **Automatic Fallback**: Ensures uploads always succeed with minimal performance impact

## Troubleshooting

If you continue to experience CORS issues:

1. **Check S3 Bucket Configuration**: Ensure the CORS configuration includes your domain
2. **Verify Presigned URLs**: Make sure the presigned URLs are generated correctly
3. **Check Browser Console**: Look for specific error messages related to CORS
4. **Test with Different Browsers**: Some browsers handle CORS differently

## Future Improvements

1. **Caching CORS Status**: Remember which endpoints had CORS issues to avoid unnecessary direct upload attempts
2. **Adaptive Strategy**: Automatically choose the best upload method based on file size and network conditions
3. **Resumable Uploads**: Implement true resumable uploads for large files to handle interruptions
