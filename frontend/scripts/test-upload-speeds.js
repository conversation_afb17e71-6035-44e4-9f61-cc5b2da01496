/**
 * Test script for measuring upload speeds with different file sizes
 * 
 * Usage:
 * node scripts/test-upload-speeds.js
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { createReadStream } = require('fs');
const { Readable } = require('stream');

// Configuration
const TEST_SIZES = [
  { name: 'small', size: 5 * 1024 * 1024 }, // 5MB
  { name: 'medium', size: 50 * 1024 * 1024 }, // 50MB
  { name: 'large', size: 200 * 1024 * 1024 }, // 200MB
];
const TEST_FILE_DIR = path.join(__dirname, '../test-files');
const CHUNK_SIZE = 120 * 1024 * 1024; // 120MB chunks
const MAX_CONCURRENT_UPLOADS = 6;

// Create test directory if it doesn't exist
if (!fs.existsSync(TEST_FILE_DIR)) {
  fs.mkdirSync(TEST_FILE_DIR, { recursive: true });
}

// Generate a random buffer of specified size
function generateRandomBuffer(size) {
  const buffer = Buffer.alloc(size);
  for (let i = 0; i < size; i += 4096) {
    // Fill with random data in chunks to avoid memory issues
    const chunkSize = Math.min(4096, size - i);
    for (let j = 0; j < chunkSize; j++) {
      buffer[i + j] = Math.floor(Math.random() * 256);
    }
  }
  return buffer;
}

// Create test files of different sizes
async function createTestFiles() {
  console.log('Creating test files...');
  
  for (const testSize of TEST_SIZES) {
    const filePath = path.join(TEST_FILE_DIR, `test-${testSize.name}.bin`);
    
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      if (stats.size === testSize.size) {
        console.log(`Test file ${filePath} already exists with correct size, skipping`);
        continue;
      }
      console.log(`Test file ${filePath} exists but has wrong size, recreating`);
    }
    
    console.log(`Creating ${testSize.name} test file (${testSize.size / (1024 * 1024)}MB)...`);
    
    // For large files, write in chunks to avoid memory issues
    if (testSize.size > 50 * 1024 * 1024) {
      const fileHandle = fs.openSync(filePath, 'w');
      const chunkSize = 10 * 1024 * 1024; // 10MB chunks for file creation
      let bytesWritten = 0;
      
      while (bytesWritten < testSize.size) {
        const currentChunkSize = Math.min(chunkSize, testSize.size - bytesWritten);
        const buffer = generateRandomBuffer(currentChunkSize);
        fs.writeSync(fileHandle, buffer, 0, buffer.length);
        bytesWritten += currentChunkSize;
        
        // Log progress
        const progress = Math.round((bytesWritten / testSize.size) * 100);
        process.stdout.write(`\rProgress: ${progress}%`);
      }
      
      fs.closeSync(fileHandle);
      console.log(`\nCreated ${testSize.name} test file: ${filePath}`);
    } else {
      // For smaller files, create in one go
      const buffer = generateRandomBuffer(testSize.size);
      fs.writeFileSync(filePath, buffer);
      console.log(`Created ${testSize.name} test file: ${filePath}`);
    }
  }
}

// Upload a file using direct method
async function uploadFileDirect(filePath, fileName) {
  console.log(`\nTesting direct upload for ${fileName}...`);
  const fileSize = fs.statSync(filePath).size;
  const fileStream = fs.createReadStream(filePath);
  
  // Mock S3 URL (replace with your actual S3 URL in production)
  const mockS3Url = 'https://example.com/upload';
  
  const startTime = Date.now();
  
  try {
    // Simulate upload (in a real scenario, you would use your actual S3 upload code)
    // This is just measuring the read speed as a baseline
    let bytesRead = 0;
    
    await new Promise((resolve, reject) => {
      fileStream.on('data', (chunk) => {
        bytesRead += chunk.length;
        const progress = Math.round((bytesRead / fileSize) * 100);
        const elapsedSeconds = (Date.now() - startTime) / 1000;
        const speed = bytesRead / elapsedSeconds / (1024 * 1024);
        process.stdout.write(`\rProgress: ${progress}% at ${speed.toFixed(2)} MB/s`);
      });
      
      fileStream.on('end', resolve);
      fileStream.on('error', reject);
    });
    
    const endTime = Date.now();
    const elapsedSeconds = (endTime - startTime) / 1000;
    const speed = fileSize / elapsedSeconds / (1024 * 1024);
    
    console.log(`\nDirect upload completed in ${elapsedSeconds.toFixed(2)}s at ${speed.toFixed(2)} MB/s`);
    return { method: 'direct', speed, elapsedSeconds };
  } catch (error) {
    console.error(`\nDirect upload failed: ${error.message}`);
    return { method: 'direct', error: error.message };
  }
}

// Upload a file using chunked method
async function uploadFileChunked(filePath, fileName) {
  console.log(`\nTesting chunked upload for ${fileName}...`);
  const fileSize = fs.statSync(filePath).size;
  
  // Calculate number of chunks
  const numChunks = Math.ceil(fileSize / CHUNK_SIZE);
  console.log(`File will be split into ${numChunks} chunks of ${CHUNK_SIZE / (1024 * 1024)}MB each`);
  
  const startTime = Date.now();
  
  try {
    let bytesUploaded = 0;
    
    // Process chunks with limited concurrency
    for (let i = 0; i < numChunks; i += MAX_CONCURRENT_UPLOADS) {
      const batchPromises = [];
      
      for (let j = 0; j < MAX_CONCURRENT_UPLOADS && i + j < numChunks; j++) {
        const chunkIndex = i + j;
        const start = chunkIndex * CHUNK_SIZE;
        const end = Math.min(fileSize, start + CHUNK_SIZE);
        const chunkSize = end - start;
        
        // Simulate chunk upload (in a real scenario, you would use your actual S3 upload code)
        batchPromises.push(
          new Promise((resolve) => {
            setTimeout(() => {
              bytesUploaded += chunkSize;
              const progress = Math.round((bytesUploaded / fileSize) * 100);
              const elapsedSeconds = (Date.now() - startTime) / 1000;
              const speed = bytesUploaded / elapsedSeconds / (1024 * 1024);
              process.stdout.write(`\rProgress: ${progress}% at ${speed.toFixed(2)} MB/s`);
              resolve();
            }, 100); // Simulate network delay
          })
        );
      }
      
      // Wait for the current batch to complete
      await Promise.all(batchPromises);
    }
    
    const endTime = Date.now();
    const elapsedSeconds = (endTime - startTime) / 1000;
    const speed = fileSize / elapsedSeconds / (1024 * 1024);
    
    console.log(`\nChunked upload completed in ${elapsedSeconds.toFixed(2)}s at ${speed.toFixed(2)} MB/s`);
    return { method: 'chunked', speed, elapsedSeconds };
  } catch (error) {
    console.error(`\nChunked upload failed: ${error.message}`);
    return { method: 'chunked', error: error.message };
  }
}

// Run the tests
async function runTests() {
  try {
    await createTestFiles();
    
    const results = [];
    
    for (const testSize of TEST_SIZES) {
      const filePath = path.join(TEST_FILE_DIR, `test-${testSize.name}.bin`);
      const fileName = `test-${testSize.name}.bin`;
      
      // Test direct upload
      const directResult = await uploadFileDirect(filePath, fileName);
      results.push({ size: testSize.name, ...directResult });
      
      // Test chunked upload
      const chunkedResult = await uploadFileChunked(filePath, fileName);
      results.push({ size: testSize.name, ...chunkedResult });
    }
    
    // Print summary
    console.log('\n=== Upload Speed Test Results ===');
    console.table(results.map(r => ({
      'File Size': r.size,
      'Method': r.method,
      'Speed (MB/s)': r.error ? 'ERROR' : r.speed.toFixed(2),
      'Time (s)': r.error ? 'ERROR' : r.elapsedSeconds.toFixed(2),
      'Error': r.error || 'None'
    })));
    
    console.log('\nTest completed!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the tests
runTests();
