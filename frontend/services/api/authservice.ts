// services/api/authService.ts
import { UserProfile } from '../../utils/auth';

// Interface for login credentials
interface LoginCredentials {
  email?: string;
  mobile_number?: string;
  password: string;
}

// Interface for signup data
interface SignupData {
  name: string;
  email: string;
  password: string;
  mobile_number?: string | null;
  dob?: string;
  marital_status?: string;
  place?: string;
  user_type: string;
}

// Interface for Clerk authentication
interface ClerkAuthData {
  clerk_token: string;
  user_type: string;
}

const TOKEN_KEY = 'jwt_token'; // Standardize token key name

const authService = {
  // Login with email/mobile and password
  login: async (credentials: LoginCredentials): Promise<any> => {
    try {
      console.log('Attempting to login with:', { ...credentials, password: '****' });

      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      console.log('Login response received:', { success: data.success });

      if (!response.ok) {
        throw new Error(data.error || 'Login failed');
      }

      // Store token in localStorage
      if (data.token) {
        console.log('Token received, storing in localStorage');
        localStorage.setItem(TOKEN_KEY, data.token);

        // Check if this is a vendor login
        if (data.user_type === 'vendor') {
          console.log('Vendor login detected, setting vendor flag');
          localStorage.setItem('is_vendor', 'true');
        }
      } else {
        console.error('No token received in login response');
      }

      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Register a new user
  signup: async (userData: SignupData): Promise<any> => {
    try {
      console.log('Attempting to register new user:', { ...userData, password: '****' });

      const response = await fetch('/api/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      console.log('Signup response received:', { success: data.success });

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed');
      }

      // Store token in localStorage
      if (data.token) {
        console.log('Token received, storing in localStorage');
        localStorage.setItem(TOKEN_KEY, data.token);
      } else {
        console.error('No token received in signup response');
      }

      return data;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  },

  // Authenticate with Clerk
  clerkAuth: async (authData: ClerkAuthData): Promise<any> => {
    try {
      console.log('Attempting Clerk authentication');

      const response = await fetch('/api/clerk-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(authData),
      });

      const data = await response.json();

      console.log('Clerk auth response received:', { success: data.success });

      if (!response.ok) {
        throw new Error(data.error || 'Clerk authentication failed');
      }

      // Store token in localStorage
      if (data.token) {
        console.log('Token received, storing in localStorage');
        localStorage.setItem(TOKEN_KEY, data.token);
      } else {
        console.error('No token received in clerk auth response');
      }

      return data;
    } catch (error) {
      console.error('Clerk auth error:', error);
      throw error;
    }
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    if (typeof window === 'undefined') return false;
    return !!localStorage.getItem(TOKEN_KEY);
  },

  // Get the current token
  getToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(TOKEN_KEY);
  },

  // Log the user out
  logout: (): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(TOKEN_KEY);
    // Also remove any other auth-related storage
    localStorage.removeItem('token'); // In case the old key name is still in use
    localStorage.removeItem('is_vendor'); // Remove vendor flag
  },

  // Check profile completion status
  checkProfile: async (): Promise<any> => {
    try {
      const token = authService.getToken();

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/check-profile', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to check profile');
      }

      return data;
    } catch (error) {
      console.error('Profile check error:', error);
      throw error;
    }
  },
};

export default authService;