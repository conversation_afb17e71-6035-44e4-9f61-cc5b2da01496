import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head />
      <body>
        <Main />
        <NextScript />
        {/* Script to remove browser extension attributes that cause hydration mismatches */}
        <script dangerouslySetInnerHTML={{
          __html: `
            (function() {
              // Run immediately to clean up before React hydration
              function cleanupExtensionAttributes() {
                // Target common extension attributes
                const attributesToRemove = [
                  'bis_skin_checked',
                  '__processed_',
                  'data-bis-'
                ];
                
                // Get all elements
                const allElements = document.querySelectorAll('*');
                
                // Remove attributes from each element
                allElements.forEach(el => {
                  for (let i = 0; i < el.attributes.length; i++) {
                    const attr = el.attributes[i];
                    for (const badAttr of attributesToRemove) {
                      if (attr.name.includes(badAttr)) {
                        el.removeAttribute(attr.name);
                        // Adjust index since we removed an attribute
                        i--;
                        break;
                      }
                    }
                  }
                });
              }
              
              // Run immediately
              cleanupExtensionAttributes();
              
              // Also run after a short delay to catch any late additions
              setTimeout(cleanupExtensionAttributes, 0);
              
              // Run again when DOM changes
              const observer = new MutationObserver(function(mutations) {
                cleanupExtensionAttributes();
              });
              
              // Start observing the document
              observer.observe(document.documentElement, {
                childList: true,
                subtree: true,
                attributes: true
              });
            })();
          `
        }} />
      </body>
    </Html>
  );
}
