const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Get the SSL certificate paths from environment variables or use default paths
const keyPath = process.env.SSL_KEY_PATH || path.join(__dirname, 'certificates', 'server.key');
const certPath = process.env.SSL_CERT_PATH || path.join(__dirname, 'certificates', 'server.crt');

// Check if SSL certificates exist
const sslOptions = {
  key: fs.existsSync(keyPath) ? fs.readFileSync(keyPath) : null,
  cert: fs.existsSync(certPath) ? fs.readFileSync(certPath) : null,
};

// Define the port to use
const port = parseInt(process.env.PORT || '3000', 10);

app.prepare().then(() => {
  // Check if SSL certificates are available
  if (!sslOptions.key || !sslOptions.cert) {
    console.error('SSL certificates not found. Please generate them first.');
    console.error(`Expected key at: ${keyPath}`);
    console.error(`Expected cert at: ${certPath}`);
    process.exit(1);
  }

  // Create HTTPS server
  createServer(sslOptions, (req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on https://localhost:${port}`);
  });
});
