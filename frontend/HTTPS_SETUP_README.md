# Setting Up HTTPS for WedZat Next.js Application

This guide provides instructions on how to set up HTTPS for your Next.js application without using Nginx.

## Prerequisites

- Node.js installed
- npm or yarn installed
- OpenSSL installed (for generating SSL certificates)

## Quick Setup

1. **Run the setup script:**

```bash
cd /home/<USER>/WEDZAT_/frontend
./setup-https.sh
```

This script will:
- Create a `certificates` directory
- Generate self-signed SSL certificates
- Verify that the custom server file exists

2. **Start the application with HTTPS:**

```bash
npm run dev
```

## Manual Setup

If you prefer to set up HTTPS manually, follow these steps:

### 1. Create SSL Certificates

```bash
# Create certificates directory
mkdir -p certificates
cd certificates

# Generate SSL certificates
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout server.key -out server.crt
```

When prompted, enter your information or use the `-subj` flag to provide information non-interactively:

```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout server.key -out server.crt -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
```

### 2. Verify Configuration Files

Ensure that the following files are properly configured:

- **server.js**: Custom server file that creates an HTTPS server
- **next.config.js**: Next.js configuration with HTTPS settings
- **package.json**: Updated scripts to use the custom server

### 3. Start the Application

```bash
# Development mode with HTTPS
npm run dev

# Production mode with HTTPS
npm run build
npm run start
```

## Production Deployment

For production, you should use certificates from a trusted certificate authority (CA) like Let's Encrypt.

1. **Obtain SSL certificates from a trusted CA**

2. **Set environment variables for certificate paths:**

```bash
export SSL_KEY_PATH=/path/to/your/production/key.pem
export SSL_CERT_PATH=/path/to/your/production/cert.pem
```

3. **Start the application in production mode:**

```bash
npm run build
npm run start
```

## Troubleshooting

### Browser Security Warnings

Self-signed certificates will trigger security warnings in browsers. This is normal for development environments. In production, use certificates from a trusted CA.

### Certificate Issues

If you encounter certificate-related errors:

```bash
# Check certificate validity
openssl x509 -in certificates/server.crt -text -noout

# Verify certificate and key match
openssl x509 -noout -modulus -in certificates/server.crt | openssl md5
openssl rsa -noout -modulus -in certificates/server.key | openssl md5
```

The MD5 hashes should match if the certificate and key are a pair.

### Port Already in Use

If port 3000 is already in use, you can change the port in `server.js` or set the `PORT` environment variable:

```bash
export PORT=3001
npm run dev
```

## Additional Options

### Using a Different Port

```bash
# Set the port environment variable
export PORT=3001
npm run dev
```

### Reverting to HTTP (Development Only)

If you need to use HTTP during development:

```bash
# Use the original Next.js development server
npm run dev:next
```

## Security Considerations

- Self-signed certificates are suitable for development but not for production
- Always use certificates from a trusted CA in production
- Keep your private keys secure and never commit them to version control
- Consider implementing HTTP to HTTPS redirection for better security
