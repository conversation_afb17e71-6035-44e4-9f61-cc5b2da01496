# Handling Large File Uploads in WedZat

This README provides instructions on how to handle large file uploads in the WedZat application without using Nginx.

## Current Configuration

The application is already configured to handle large file uploads directly through Next.js:

1. **Next.js Configuration**:
   - `bodyParser` is disabled to allow handling large files
   - `responseLimit` is set to `false` to remove response size limits
   - CORS headers are properly configured

2. **Upload Proxy API Route**:
   - Located at `frontend/app/api/upload-proxy/route.ts`
   - Uses `axios` with `maxBodyLength: Infinity` and `maxContentLength: Infinity`
   - Sets a 1-hour timeout for large file uploads

## Testing Large File Uploads

To test large file uploads:

1. Navigate to the upload feature in the application
2. Select a large file (e.g., a video file)
3. Monitor the upload progress
4. Check the browser console and server logs for any errors

## Troubleshooting

If you encounter issues with large file uploads:

### Client-Side Issues

1. **Browser Limitations**:
   - Some browsers have built-in limitations on file upload sizes
   - Try using Chrome or Firefox for large uploads

2. **Network Timeouts**:
   - Ensure your network connection is stable
   - For very large files, consider using a wired connection

### Server-Side Issues

1. **Memory Limitations**:
   - If the server runs out of memory, increase the Node.js memory limit:
     ```bash
     export NODE_OPTIONS="--max-old-space-size=8192"
     ```

2. **Disk Space**:
   - Ensure the server has enough disk space for temporary file storage

3. **AWS S3 Limitations**:
   - Check S3 bucket policies and permissions
   - For files larger than 5GB, consider using multipart uploads

## Implementing Chunked Uploads (Optional)

For extremely large files, consider implementing chunked uploads:

1. Split large files into smaller chunks on the client side
2. Upload each chunk separately
3. Reassemble the chunks on the server side

Example libraries for chunked uploads:
- [tus-js-client](https://github.com/tus/tus-js-client)
- [uppy](https://uppy.io/)

## Monitoring

Monitor your application's performance during large file uploads:

1. **Memory Usage**:
   ```bash
   watch -n 1 'free -m'
   ```

2. **CPU Usage**:
   ```bash
   top
   ```

3. **Disk Usage**:
   ```bash
   df -h
   ```

## Best Practices

1. **Validate Files Before Upload**:
   - Check file size on the client side before uploading
   - Validate file types to prevent malicious uploads

2. **Implement Progress Indicators**:
   - Show upload progress to users
   - Provide estimated time remaining for large uploads

3. **Handle Errors Gracefully**:
   - Provide clear error messages
   - Offer retry options for failed uploads

4. **Optimize File Sizes**:
   - Compress images and videos when possible
   - Consider server-side processing for large media files

## Additional Resources

- [Next.js API Routes Documentation](https://nextjs.org/docs/api-routes/introduction)
- [AWS S3 Upload Best Practices](https://docs.aws.amazon.com/AmazonS3/latest/userguide/upload-objects.html)
- [Handling File Uploads in React](https://react-dropzone.js.org/)
